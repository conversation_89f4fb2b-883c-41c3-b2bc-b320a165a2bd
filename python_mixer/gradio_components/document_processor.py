"""
Document Processing Module for Fulmark.pl HVAC CRM
==================================================

Advanced PDF processing and OCR capabilities for invoice and document analysis.
Supports text extraction, OCR for scanned documents, and metadata extraction.

Features:
- PDF text extraction with multiple fallback methods
- OCR processing for scanned documents using Tesseract
- Invoice detection and classification
- Metadata extraction (dates, amounts, vendor information)
- Integration with HVAC supplier databases (LG, Daikin)
- Document validation and quality assessment
"""

import io
import re
import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from loguru import logger

# PDF processing libraries
try:
    import PyPDF2
    import pdfplumber
    PDF_AVAILABLE = True
except ImportError:
    logger.warning("PDF libraries not available. Install: pip install PyPDF2 pdfplumber")
    PDF_AVAILABLE = False

# OCR libraries
try:
    import pytesseract
    from PIL import Image
    import pdf2image
    OCR_AVAILABLE = True
except ImportError:
    logger.warning("OCR libraries not available. Install: pip install pytesseract Pillow pdf2image")
    OCR_AVAILABLE = False

# Data processing
import pandas as pd
import numpy as np


class DocumentProcessor:
    """
    Advanced document processor for HVAC CRM operations.
    
    Handles PDF text extraction, OCR processing, and document analysis
    with specific focus on invoice processing for Fulmark.pl operations.
    """
    
    def __init__(self):
        """Initialize the document processor."""
        self.supported_formats = ['.pdf', '.png', '.jpg', '.jpeg', '.tiff']
        self.hvac_suppliers = self._load_hvac_suppliers()
        self.invoice_patterns = self._load_invoice_patterns()
        
        # OCR configuration
        self.ocr_config = '--oem 3 --psm 6 -l pol+eng'  # Polish and English
        
        logger.info("Document processor initialized")
    
    def _load_hvac_suppliers(self) -> Dict[str, Dict]:
        """Load known HVAC suppliers database."""
        return {
            "lg": {
                "name": "LG Electronics",
                "aliases": ["lg", "lg electronics", "lg polska", "lg air conditioning"],
                "tax_id": "PL1234567890",
                "category": "equipment_manufacturer",
                "products": ["klimatyzatory", "pompy ciepła", "systemy hvac"]
            },
            "daikin": {
                "name": "Daikin Europe",
                "aliases": ["daikin", "daikin europe", "daikin polska", "daikin air conditioning"],
                "tax_id": "PL0987654321",
                "category": "equipment_manufacturer", 
                "products": ["klimatyzatory", "pompy ciepła", "systemy wentylacji"]
            },
            "elektro_parts": {
                "name": "Elektro-Parts Sp. z o.o.",
                "aliases": ["elektro-parts", "elektro parts", "e-parts"],
                "tax_id": "PL1122334455",
                "category": "parts_supplier",
                "products": ["części zamienne", "komponenty elektryczne", "filtry"]
            },
            "hvac_service": {
                "name": "HVAC Service Warszawa",
                "aliases": ["hvac service", "hvac-service", "hvac warszawa"],
                "tax_id": "PL5566778899",
                "category": "service_provider",
                "products": ["serwis", "konserwacja", "naprawy"]
            }
        }
    
    def _load_invoice_patterns(self) -> Dict[str, str]:
        """Load regex patterns for invoice data extraction."""
        return {
            "invoice_number": r"(?:faktura|invoice|nr|number|numer)[\s:]*([A-Z0-9\-\/]+)",
            "date": r"(?:data|date|dnia)[\s:]*(\d{1,2}[.\-\/]\d{1,2}[.\-\/]\d{2,4})",
            "total_amount": r"(?:suma|total|razem|do zapłaty)[\s:]*(\d+[,.]?\d*)\s*(?:zł|pln|eur|€)",
            "tax_id": r"(?:nip|tax id|vat)[\s:]*([0-9\-\s]{10,15})",
            "vendor_name": r"(?:sprzedawca|vendor|firma)[\s:]*([A-ZĄĆĘŁŃÓŚŹŻ][a-ząćęłńóśźż\s\.\-]+)",
            "line_items": r"(\d+)\s+([A-Za-ząćęłńóśźż\s\-]+)\s+(\d+[,.]?\d*)\s+(\d+[,.]?\d*)",
            "payment_terms": r"(?:termin płatności|payment terms)[\s:]*(\d+)\s*(?:dni|days)"
        }
    
    def process_document(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """
        Process a document and extract relevant information.
        
        Args:
            file_content: Raw file content as bytes
            filename: Original filename
            
        Returns:
            Dict containing extracted information and analysis results
        """
        try:
            file_ext = Path(filename).suffix.lower()
            
            if file_ext not in self.supported_formats:
                return {
                    "success": False,
                    "error": f"Nieobsługiwany format pliku: {file_ext}",
                    "supported_formats": self.supported_formats
                }
            
            result = {
                "filename": filename,
                "file_size": len(file_content),
                "processed_at": datetime.now().isoformat(),
                "success": True,
                "document_type": "unknown",
                "extracted_text": "",
                "metadata": {},
                "invoice_data": {},
                "supplier_info": {},
                "confidence_score": 0.0
            }
            
            # Extract text based on file type
            if file_ext == '.pdf':
                text_result = self._extract_pdf_text(file_content)
                result.update(text_result)
            else:
                # Image files - use OCR
                text_result = self._extract_image_text(file_content)
                result.update(text_result)
            
            # Analyze extracted text
            if result["extracted_text"]:
                analysis = self._analyze_document_content(result["extracted_text"])
                result.update(analysis)
            
            return result
            
        except Exception as e:
            logger.error(f"Document processing error: {e}")
            return {
                "success": False,
                "error": str(e),
                "filename": filename
            }
    
    def _extract_pdf_text(self, pdf_content: bytes) -> Dict[str, Any]:
        """Extract text from PDF using multiple methods."""
        if not PDF_AVAILABLE:
            return {
                "success": False,
                "error": "PDF processing libraries not available"
            }
        
        text = ""
        method_used = "none"
        
        try:
            # Method 1: pdfplumber (best for structured PDFs)
            with pdfplumber.open(io.BytesIO(pdf_content)) as pdf:
                pages_text = []
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        pages_text.append(page_text)
                
                if pages_text:
                    text = "\n".join(pages_text)
                    method_used = "pdfplumber"
        
        except Exception as e:
            logger.warning(f"pdfplumber failed: {e}")
        
        # Method 2: PyPDF2 (fallback)
        if not text:
            try:
                pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_content))
                pages_text = []
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text:
                        pages_text.append(page_text)
                
                if pages_text:
                    text = "\n".join(pages_text)
                    method_used = "PyPDF2"
            
            except Exception as e:
                logger.warning(f"PyPDF2 failed: {e}")
        
        # Method 3: OCR (for scanned PDFs)
        if not text or len(text.strip()) < 50:
            try:
                ocr_result = self._pdf_to_ocr(pdf_content)
                if ocr_result["success"]:
                    text = ocr_result["extracted_text"]
                    method_used = "OCR"
            except Exception as e:
                logger.warning(f"OCR fallback failed: {e}")
        
        return {
            "extracted_text": text,
            "extraction_method": method_used,
            "text_length": len(text),
            "success": bool(text)
        }
    
    def _pdf_to_ocr(self, pdf_content: bytes) -> Dict[str, Any]:
        """Convert PDF to images and apply OCR."""
        if not OCR_AVAILABLE:
            return {
                "success": False,
                "error": "OCR libraries not available"
            }
        
        try:
            # Convert PDF to images
            images = pdf2image.convert_from_bytes(pdf_content, dpi=300)
            
            extracted_texts = []
            for i, image in enumerate(images):
                # Apply OCR to each page
                page_text = pytesseract.image_to_string(image, config=self.ocr_config)
                if page_text.strip():
                    extracted_texts.append(f"--- Strona {i+1} ---\n{page_text}")
            
            combined_text = "\n\n".join(extracted_texts)
            
            return {
                "success": True,
                "extracted_text": combined_text,
                "pages_processed": len(images),
                "method": "OCR"
            }
            
        except Exception as e:
            logger.error(f"PDF to OCR conversion failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _extract_image_text(self, image_content: bytes) -> Dict[str, Any]:
        """Extract text from image using OCR."""
        if not OCR_AVAILABLE:
            return {
                "success": False,
                "error": "OCR libraries not available"
            }
        
        try:
            # Load image
            image = Image.open(io.BytesIO(image_content))
            
            # Apply OCR
            text = pytesseract.image_to_string(image, config=self.ocr_config)
            
            return {
                "extracted_text": text,
                "extraction_method": "OCR",
                "text_length": len(text),
                "success": bool(text.strip())
            }
            
        except Exception as e:
            logger.error(f"Image OCR failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _analyze_document_content(self, text: str) -> Dict[str, Any]:
        """Analyze document content to determine type and extract data."""
        analysis = {
            "document_type": "unknown",
            "confidence_score": 0.0,
            "invoice_data": {},
            "supplier_info": {},
            "metadata": {}
        }

        # Determine document type
        doc_type, confidence = self._classify_document(text)
        analysis["document_type"] = doc_type
        analysis["confidence_score"] = confidence

        # Extract invoice data if it's an invoice
        if doc_type == "invoice":
            invoice_data = self._extract_invoice_data(text)
            analysis["invoice_data"] = invoice_data

            # Identify supplier
            supplier_info = self._identify_supplier(text, invoice_data)
            analysis["supplier_info"] = supplier_info

        # Extract general metadata
        metadata = self._extract_metadata(text)
        analysis["metadata"] = metadata

        return analysis

    def _classify_document(self, text: str) -> Tuple[str, float]:
        """Classify document type based on content."""
        text_lower = text.lower()

        # Invoice indicators
        invoice_keywords = [
            "faktura", "invoice", "rachunek", "bill",
            "do zapłaty", "suma", "total", "nip", "tax id",
            "sprzedawca", "vendor", "nabywca", "buyer"
        ]

        # Quote indicators
        quote_keywords = [
            "oferta", "quote", "wycena", "quotation",
            "propozycja", "proposal", "kosztorys", "estimate"
        ]

        # Service report indicators
        service_keywords = [
            "protokół", "raport", "serwis", "service",
            "naprawa", "repair", "konserwacja", "maintenance",
            "przegląd", "inspection"
        ]

        # Calculate scores
        invoice_score = sum(1 for keyword in invoice_keywords if keyword in text_lower)
        quote_score = sum(1 for keyword in quote_keywords if keyword in text_lower)
        service_score = sum(1 for keyword in service_keywords if keyword in text_lower)

        # Determine type and confidence
        max_score = max(invoice_score, quote_score, service_score)

        if max_score == 0:
            return "unknown", 0.0

        confidence = min(max_score / 5.0, 1.0)  # Normalize to 0-1

        if invoice_score == max_score:
            return "invoice", confidence
        elif quote_score == max_score:
            return "quote", confidence
        elif service_score == max_score:
            return "service_report", confidence
        else:
            return "unknown", 0.0

    def _extract_invoice_data(self, text: str) -> Dict[str, Any]:
        """Extract structured invoice data from text."""
        invoice_data = {
            "invoice_number": None,
            "date": None,
            "total_amount": None,
            "currency": "PLN",
            "tax_id": None,
            "vendor_name": None,
            "line_items": [],
            "payment_terms": None,
            "extracted_fields": {}
        }

        # Extract using regex patterns
        for field, pattern in self.invoice_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                if field == "line_items":
                    # Process line items separately
                    invoice_data["line_items"] = self._process_line_items(matches)
                else:
                    # Take first match for single-value fields
                    value = matches[0] if isinstance(matches[0], str) else matches[0][0]
                    invoice_data[field] = value.strip()

                invoice_data["extracted_fields"][field] = matches

        # Post-process extracted data
        invoice_data = self._post_process_invoice_data(invoice_data)

        return invoice_data

    def _process_line_items(self, line_matches: List[Tuple]) -> List[Dict]:
        """Process extracted line items into structured format."""
        line_items = []

        for match in line_matches:
            if len(match) >= 4:
                item = {
                    "position": match[0],
                    "description": match[1].strip(),
                    "quantity": self._parse_number(match[2]),
                    "unit_price": self._parse_number(match[3]),
                    "total_price": None
                }

                # Calculate total if possible
                if item["quantity"] and item["unit_price"]:
                    item["total_price"] = item["quantity"] * item["unit_price"]

                line_items.append(item)

        return line_items

    def _parse_number(self, text: str) -> Optional[float]:
        """Parse number from text, handling Polish decimal format."""
        try:
            # Replace comma with dot for decimal
            cleaned = text.replace(",", ".").replace(" ", "")
            return float(cleaned)
        except (ValueError, AttributeError):
            return None

    def _post_process_invoice_data(self, data: Dict) -> Dict:
        """Post-process and validate extracted invoice data."""
        # Parse and validate date
        if data.get("date"):
            data["date"] = self._parse_date(data["date"])

        # Parse and validate amount
        if data.get("total_amount"):
            data["total_amount"] = self._parse_number(data["total_amount"])

        # Clean and validate tax ID
        if data.get("tax_id"):
            data["tax_id"] = self._clean_tax_id(data["tax_id"])

        # Clean vendor name
        if data.get("vendor_name"):
            data["vendor_name"] = self._clean_vendor_name(data["vendor_name"])

        return data

    def _parse_date(self, date_str: str) -> Optional[str]:
        """Parse date string into ISO format."""
        try:
            # Common Polish date formats
            formats = ["%d.%m.%Y", "%d-%m-%Y", "%d/%m/%Y", "%Y-%m-%d"]

            for fmt in formats:
                try:
                    parsed_date = datetime.strptime(date_str.strip(), fmt)
                    return parsed_date.strftime("%Y-%m-%d")
                except ValueError:
                    continue

            return date_str  # Return original if parsing fails
        except Exception:
            return None

    def _clean_tax_id(self, tax_id: str) -> str:
        """Clean and format tax ID."""
        # Remove spaces and hyphens, keep only digits
        cleaned = re.sub(r'[^\d]', '', tax_id)

        # Format Polish NIP (10 digits)
        if len(cleaned) == 10:
            return f"{cleaned[:3]}-{cleaned[3:6]}-{cleaned[6:8]}-{cleaned[8:]}"

        return cleaned

    def _clean_vendor_name(self, name: str) -> str:
        """Clean vendor name."""
        # Remove extra whitespace and common prefixes
        cleaned = re.sub(r'\s+', ' ', name.strip())

        # Remove common business suffixes for matching
        suffixes = ["sp. z o.o.", "s.a.", "spółka z o.o.", "ltd", "llc", "inc"]
        for suffix in suffixes:
            cleaned = re.sub(rf'\s+{re.escape(suffix)}\.?$', '', cleaned, flags=re.IGNORECASE)

        return cleaned.strip()

    def _identify_supplier(self, text: str, invoice_data: Dict) -> Dict[str, Any]:
        """Identify supplier from known HVAC suppliers database."""
        text_lower = text.lower()
        vendor_name = invoice_data.get("vendor_name", "").lower()
        tax_id = invoice_data.get("tax_id", "")

        supplier_info = {
            "identified": False,
            "supplier_id": None,
            "supplier_name": None,
            "category": None,
            "confidence": 0.0,
            "match_method": None
        }

        # Try to match by tax ID first (most reliable)
        if tax_id:
            for supplier_id, supplier in self.hvac_suppliers.items():
                if supplier.get("tax_id") == tax_id:
                    supplier_info.update({
                        "identified": True,
                        "supplier_id": supplier_id,
                        "supplier_name": supplier["name"],
                        "category": supplier["category"],
                        "confidence": 1.0,
                        "match_method": "tax_id"
                    })
                    return supplier_info

        # Try to match by name/aliases
        best_match = None
        best_score = 0.0

        for supplier_id, supplier in self.hvac_suppliers.items():
            # Check main name
            name_score = self._calculate_name_similarity(vendor_name, supplier["name"].lower())

            # Check aliases
            alias_scores = [
                self._calculate_name_similarity(vendor_name, alias.lower())
                for alias in supplier["aliases"]
            ]

            max_alias_score = max(alias_scores) if alias_scores else 0.0
            final_score = max(name_score, max_alias_score)

            if final_score > best_score and final_score > 0.6:  # Threshold for matching
                best_score = final_score
                best_match = (supplier_id, supplier)

        if best_match:
            supplier_id, supplier = best_match
            supplier_info.update({
                "identified": True,
                "supplier_id": supplier_id,
                "supplier_name": supplier["name"],
                "category": supplier["category"],
                "confidence": best_score,
                "match_method": "name_similarity"
            })

        return supplier_info

    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between two names."""
        if not name1 or not name2:
            return 0.0

        # Simple word-based similarity
        words1 = set(name1.split())
        words2 = set(name2.split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def _extract_metadata(self, text: str) -> Dict[str, Any]:
        """Extract general metadata from document."""
        metadata = {
            "text_length": len(text),
            "word_count": len(text.split()),
            "language": self._detect_language(text),
            "contains_amounts": bool(re.search(r'\d+[,.]?\d*\s*(?:zł|pln|eur|€)', text, re.IGNORECASE)),
            "contains_dates": bool(re.search(r'\d{1,2}[.\-\/]\d{1,2}[.\-\/]\d{2,4}', text)),
            "contains_tax_id": bool(re.search(r'(?:nip|tax id)[\s:]*[0-9\-\s]{10,15}', text, re.IGNORECASE))
        }

        return metadata

    def _detect_language(self, text: str) -> str:
        """Simple language detection based on common words."""
        polish_words = ["i", "w", "na", "z", "do", "się", "że", "nie", "jest", "to", "faktura", "zł"]
        english_words = ["the", "and", "in", "to", "of", "a", "is", "it", "you", "that", "invoice", "total"]

        text_lower = text.lower()
        polish_count = sum(1 for word in polish_words if word in text_lower)
        english_count = sum(1 for word in english_words if word in text_lower)

        if polish_count > english_count:
            return "polish"
        elif english_count > polish_count:
            return "english"
        else:
            return "unknown"
