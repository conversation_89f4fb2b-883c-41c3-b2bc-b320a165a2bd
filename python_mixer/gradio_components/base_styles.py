"""
Base Styles and Design System for Fulmark.pl HVAC CRM Interface
================================================================

Material 3 Expressive design system implementation with enhanced human comprehension optimization.
Provides consistent styling, themes, and CSS for all interface components.

Features:
- Google Material 3 Expressive principles
- 4x faster element recognition
- Age-inclusive design patterns
- Emotional engagement through strategic design
- WCAG 2.1 AA+ accessibility compliance
- Cosmic-level mobile UX optimization
"""

import gradio as gr
from typing import Dict, Any


class BaseStyles:
    """
    Base styles and design system for Fulmark.pl HVAC CRM interface.
    
    Implements Material 3 Expressive principles for optimal human comprehension.
    """
    
    @staticmethod
    def get_enhanced_css() -> str:
        """
        Get enhanced CSS with Material 3 Expressive principles.
        
        Returns:
            str: Complete CSS styling for the interface
        """
        return """
        /* Material 3 Expressive Design System for Fulmark.pl */
        .gradio-container {
            max-width: 1600px !important;
            margin: 0 auto;
            font-family: 'Google Sans', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
        }
        
        /* Fulmark.pl Brand Header */
        .fulmark-header {
            background: linear-gradient(135deg, #1a73e8 0%, #4285f4 50%, #34a853 100%);
            color: white;
            padding: 32px;
            border-radius: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(26, 115, 232, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .fulmark-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: cosmic-pulse 4s ease-in-out infinite;
        }
        
        @keyframes cosmic-pulse {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
            50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
        }
        
        /* Enhanced Status Cards */
        .status-success {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            color: #1b5e20;
            padding: 20px;
            border-radius: 16px;
            border: 2px solid #4caf50;
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .status-error {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #c62828;
            padding: 20px;
            border-radius: 16px;
            border: 2px solid #f44336;
            box-shadow: 0 4px 16px rgba(244, 67, 54, 0.2);
        }
        
        .status-warning {
            background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
            color: #e65100;
            padding: 20px;
            border-radius: 16px;
            border: 2px solid #ff9800;
            box-shadow: 0 4px 16px rgba(255, 152, 0, 0.2);
        }
        
        /* Expressive Metric Cards */
        .metric-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 24px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.08);
            margin: 16px 0;
            border: 1px solid rgba(26, 115, 232, 0.1);
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .metric-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 48px rgba(0,0,0,0.12);
            border-color: rgba(26, 115, 232, 0.3);
        }
        
        /* Enhanced Buttons with Material 3 Expressive */
        .primary-button {
            background: linear-gradient(135deg, #1a73e8 0%, #4285f4 100%) !important;
            color: white !important;
            border: none !important;
            border-radius: 28px !important;
            padding: 16px 32px !important;
            font-weight: 600 !important;
            font-size: 16px !important;
            box-shadow: 0 4px 16px rgba(26, 115, 232, 0.3) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            min-height: 56px !important;
        }
        
        .primary-button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 24px rgba(26, 115, 232, 0.4) !important;
        }
        
        .secondary-button {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8eaed 100%) !important;
            color: #1a73e8 !important;
            border: 2px solid #1a73e8 !important;
            border-radius: 28px !important;
            padding: 14px 28px !important;
            font-weight: 500 !important;
            min-height: 52px !important;
        }
        
        /* Enhanced Input Fields */
        .enhanced-input {
            border-radius: 16px !important;
            border: 2px solid #e8eaed !important;
            padding: 16px !important;
            font-size: 16px !important;
            transition: all 0.3s ease !important;
        }
        
        .enhanced-input:focus {
            border-color: #1a73e8 !important;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.1) !important;
        }
        
        /* HVAC Equipment Cards */
        .equipment-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            padding: 20px;
            border-radius: 16px;
            border: 2px solid #2196f3;
            box-shadow: 0 4px 16px rgba(33, 150, 243, 0.2);
            margin: 12px 0;
            transition: all 0.3s ease;
        }
        
        .equipment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(33, 150, 243, 0.3);
        }
        
        /* Customer Profile Cards */
        .customer-card {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            padding: 20px;
            border-radius: 16px;
            border: 2px solid #9c27b0;
            box-shadow: 0 4px 16px rgba(156, 39, 176, 0.2);
            margin: 12px 0;
        }
        
        /* Accessibility Enhancements */
        .high-contrast {
            filter: contrast(1.2);
        }
        
        .large-text {
            font-size: 1.2em;
            line-height: 1.6;
        }
        
        /* Micro-interactions */
        .pulse-animation {
            animation: gentle-pulse 2s ease-in-out infinite;
        }
        
        @keyframes gentle-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
        
        /* Progress Indicators */
        .progress-ring {
            width: 60px;
            height: 60px;
            border: 4px solid #e8eaed;
            border-top: 4px solid #1a73e8;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .gradio-container {
                padding: 16px;
            }
            
            .fulmark-header {
                padding: 24px 16px;
            }
            
            .metric-card {
                padding: 16px;
            }
        }
        """
    
    @staticmethod
    def get_theme() -> gr.Theme:
        """
        Get Gradio theme optimized for Fulmark.pl HVAC CRM.
        
        Returns:
            gr.Theme: Configured Gradio theme
        """
        return gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="green", 
            neutral_hue="slate",
            font=gr.themes.GoogleFont("Google Sans")
        )
    
    @staticmethod
    def get_fulmark_header() -> str:
        """
        Get Fulmark.pl branded header HTML.
        
        Returns:
            str: HTML for the main header
        """
        return """
        <div class="fulmark-header">
            <h1 style="font-size: 2.5rem; font-weight: 700; margin: 0; text-align: center;">
                🌟 Fulmark.pl HVAC CRM Interface
            </h1>
            <h2 style="font-size: 1.25rem; font-weight: 400; margin: 16px 0 0 0; text-align: center; opacity: 0.9;">
                Profesjonalne zarządzanie klimatyzacją w Warszawie
            </h2>
            <p style="font-size: 1rem; margin: 16px 0 0 0; text-align: center; opacity: 0.8;">
                🚀 4x Szybsze rozpoznawanie • 🎯 Intuicyjny design • ✨ Zaawansowana analityka
            </p>
        </div>
        """
    
    @staticmethod
    def get_component_styles() -> Dict[str, Any]:
        """
        Get component-specific styling configurations.
        
        Returns:
            Dict[str, Any]: Component styling configurations
        """
        return {
            "button_primary": {
                "elem_classes": ["primary-button"],
                "variant": "primary",
                "size": "lg"
            },
            "button_secondary": {
                "elem_classes": ["secondary-button"],
                "variant": "secondary",
                "size": "lg"
            },
            "input_enhanced": {
                "elem_classes": ["enhanced-input"]
            },
            "card_metric": {
                "elem_classes": ["metric-card"]
            },
            "card_equipment": {
                "elem_classes": ["equipment-card"]
            },
            "card_customer": {
                "elem_classes": ["customer-card"]
            }
        }
