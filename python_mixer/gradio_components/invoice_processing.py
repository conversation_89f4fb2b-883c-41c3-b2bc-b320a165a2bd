"""
Invoice Processing Component for Fulmark.pl HVAC CRM Interface
=============================================================

Advanced invoice processing interface with AI-powered document analysis,
automatic data extraction, and financial tracking for HVAC operations.

Features:
- PDF and image invoice processing with OCR
- Automatic invoice data extraction and validation
- Supplier identification and categorization
- Financial tracking and expense analysis
- Integration with customer profiles
- Real-time processing status and notifications
"""

import gradio as gr
import json
import time
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from loguru import logger

from .base_styles import BaseStyles
from .document_processor import DocumentProcessor
from .invoice_processing_helpers import InvoiceProcessingHelpers


class InvoiceProcessingComponent:
    """
    Invoice processing component for HVAC CRM operations.
    
    Provides comprehensive invoice processing capabilities with AI-powered analysis
    optimized for Fulmark.pl financial operations and supplier management.
    """
    
    def __init__(self):
        self.styles = BaseStyles()
        self.document_processor = DocumentProcessor()
        self.processed_invoices = []
        self.financial_data = self._initialize_financial_data()
        
    def create_interface(self) -> gr.Column:
        """
        Create the invoice processing interface.
        
        Returns:
            gr.Column: Complete invoice processing interface
        """
        with gr.Column() as interface:
            # Header and description
            gr.Markdown("""
            ### 💰 Inteligentne Przetwarzanie Faktur - Fulmark.pl
            
            **Zaawansowana analiza dokumentów finansowych:**
            - 📄 **Przetwarzanie PDF i OCR** - automatyczna ekstrakcja tekstu
            - 🤖 **AI-powered analiza** - rozpoznawanie faktur i danych
            - 🏭 **Identyfikacja dostawców** - LG, Daikin, części zamienne
            - 💼 **Kategoryzacja wydatków** - sprzęt, części, serwis, media
            - 📊 **Śledzenie finansowe** - historia kosztów per klient
            - ⚡ **Automatyczne powiadomienia** - status przetwarzania
            """)
            
            with gr.Row():
                with gr.Column(scale=2):
                    # File upload section
                    gr.Markdown("#### 📁 Upload Faktury")
                    
                    invoice_file = gr.File(
                        label="Wybierz plik faktury (PDF, PNG, JPG)",
                        file_types=[".pdf", ".png", ".jpg", ".jpeg", ".tiff"],
                        file_count="single"
                    )
                    
                    # Processing options
                    with gr.Accordion("⚙️ Opcje Przetwarzania", open=True):
                        with gr.Row():
                            auto_categorize = gr.Checkbox(
                                label="🏷️ Automatyczna kategoryzacja",
                                value=True,
                                info="Automatycznie kategoryzuj wydatki"
                            )
                            validate_supplier = gr.Checkbox(
                                label="🏭 Walidacja dostawcy",
                                value=True,
                                info="Sprawdź w bazie dostawców HVAC"
                            )
                        
                        with gr.Row():
                            extract_line_items = gr.Checkbox(
                                label="📋 Pozycje faktury",
                                value=True,
                                info="Wyodrębnij szczegółowe pozycje"
                            )
                            ocr_fallback = gr.Checkbox(
                                label="👁️ OCR dla skanów",
                                value=True,
                                info="Użyj OCR dla zeskanowanych dokumentów"
                            )
                    
                    # Customer association
                    with gr.Row():
                        customer_select = gr.Dropdown(
                            choices=self._get_customer_list(),
                            label="👤 Przypisz do klienta",
                            value=None,
                            allow_custom_value=True,
                            info="Opcjonalne - przypisz fakturę do klienta"
                        )
                        
                        expense_category = gr.Dropdown(
                            choices=[
                                "🔧 Sprzęt HVAC",
                                "🔩 Części zamienne", 
                                "⚡ Serwis i naprawy",
                                "🏢 Media i administracja",
                                "🚚 Transport i logistyka",
                                "📚 Szkolenia i certyfikaty",
                                "💼 Inne wydatki"
                            ],
                            label="📊 Kategoria wydatku",
                            value="🔧 Sprzęt HVAC"
                        )
                    
                    # Action buttons
                    with gr.Row():
                        process_btn = gr.Button(
                            "🔍 Przetwórz Fakturę",
                            **self.styles.get_component_styles()["button_primary"]
                        )
                        save_btn = gr.Button(
                            "💾 Zapisz do Systemu",
                            **self.styles.get_component_styles()["button_secondary"]
                        )
                        clear_btn = gr.Button(
                            "🗑️ Wyczyść",
                            **self.styles.get_component_styles()["button_secondary"]
                        )
                
                with gr.Column(scale=3):
                    # Processing status
                    processing_status = gr.HTML(
                        value=self._get_ready_status(),
                        label="Status Przetwarzania"
                    )
                    
                    # Results tabs
                    with gr.Tabs():
                        with gr.Tab("📊 Wyniki Analizy"):
                            invoice_results = gr.JSON(
                                label="Wyodrębnione Dane Faktury",
                                value={},
                                **self.styles.get_component_styles()["card_metric"]
                            )
                        
                        with gr.Tab("🏭 Informacje o Dostawcy"):
                            supplier_info = gr.HTML(
                                value="Informacje o dostawcy pojawią się po analizie...",
                                **self.styles.get_component_styles()["card_metric"]
                            )
                        
                        with gr.Tab("💰 Analiza Finansowa"):
                            financial_analysis = gr.HTML(
                                value="Analiza finansowa pojawi się po przetworzeniu...",
                                **self.styles.get_component_styles()["card_metric"]
                            )
                        
                        with gr.Tab("📈 Wizualizacje"):
                            financial_charts = gr.Plot(
                                label="Wykresy Finansowe",
                                value=self._create_empty_financial_plot(),
                                **self.styles.get_component_styles()["card_metric"]
                            )
            
            # Invoice history section
            with gr.Accordion("📚 Historia Faktur", open=False):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("**Ostatnio przetworzone faktury:**")
                        invoice_history = gr.HTML(
                            value=self._get_invoice_history(),
                            **self.styles.get_component_styles()["card_metric"]
                        )
                    
                    with gr.Column():
                        gr.Markdown("**Statystyki miesięczne:**")
                        monthly_stats = gr.HTML(
                            value=self._get_monthly_stats(),
                            **self.styles.get_component_styles()["card_metric"]
                        )
            
            # Event handlers
            process_btn.click(
                fn=self._process_invoice,
                inputs=[invoice_file, auto_categorize, validate_supplier, extract_line_items, 
                       ocr_fallback, customer_select, expense_category],
                outputs=[processing_status, invoice_results, supplier_info, financial_analysis, financial_charts]
            )
            
            save_btn.click(
                fn=self._save_invoice_to_system,
                inputs=[invoice_results, customer_select, expense_category],
                outputs=[processing_status, invoice_history, monthly_stats]
            )
            
            clear_btn.click(
                fn=lambda: (None, None, "🔧 Sprzęt HVAC"),
                outputs=[invoice_file, customer_select, expense_category]
            )
        
        return interface
    
    def _initialize_financial_data(self) -> Dict:
        """Initialize financial tracking data."""
        return {
            "monthly_expenses": {},
            "supplier_totals": {},
            "category_breakdown": {},
            "customer_costs": {},
            "total_processed": 0
        }
    
    def _get_customer_list(self) -> List[str]:
        """Get list of customers for dropdown."""
        return [
            "Jan Kowalski - ul. Testowa 123",
            "Anna Nowak - ul. Przykładowa 456", 
            "Piotr Wiśniewski - ul. Główna 789",
            "Firma ABC Sp. z o.o.",
            "Hotel Warszawa",
            "Biuro Rachunkowe XYZ"
        ]
    
    def _get_ready_status(self) -> str:
        """Get ready status HTML."""
        return """
        <div class="metric-card">
            <h4 style="margin: 0 0 12px 0; color: #1a73e8;">💰 Status Przetwarzania Faktur</h4>
            <p style="margin: 0; color: #666;">Gotowy do analizy dokumentów finansowych z AI</p>
            <div style="margin-top: 12px; padding: 8px; background: #e3f2fd; border-radius: 8px; font-size: 0.9em;">
                💡 <strong>Obsługiwane formaty:</strong> PDF, PNG, JPG, TIFF • OCR dla skanów • Automatyczna kategoryzacja
            </div>
        </div>
        """
    
    def _create_empty_financial_plot(self) -> go.Figure:
        """Create empty financial plot."""
        fig = go.Figure()
        fig.add_trace(go.Bar(
            x=['Sprzęt', 'Części', 'Serwis', 'Media'],
            y=[0, 0, 0, 0],
            marker_color=['#1a73e8', '#4caf50', '#ff9800', '#9c27b0']
        ))
        fig.update_layout(
            title="💰 Wydatki według Kategorii",
            xaxis_title="Kategorie",
            yaxis_title="Kwota (PLN)",
            template="plotly_white",
            height=400,
            font=dict(family="Google Sans, Roboto, sans-serif"),
            title_font=dict(size=16, color="#1a73e8"),
            plot_bgcolor='rgba(248,249,255,0.8)',
            paper_bgcolor='rgba(255,255,255,0.9)'
        )
        return fig
    
    def _get_invoice_history(self) -> str:
        """Get invoice history HTML."""
        return """
        <div style="padding: 16px; background: #f8f9fa; border-radius: 8px;">
            <p style="margin: 0; color: #666; text-align: center;">
                Brak przetworzonych faktur.<br>
                Prześlij pierwszą fakturę aby zobaczyć historię.
            </p>
        </div>
        """
    
    def _get_monthly_stats(self) -> str:
        """Get monthly statistics HTML."""
        current_month = datetime.now().strftime('%B %Y')
        return f"""
        <div style="padding: 16px; background: #f8f9fa; border-radius: 8px;">
            <h5 style="margin: 0 0 12px 0; color: #1a73e8;">{current_month}</h5>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.9em;">
                <div><strong>Faktury:</strong> 0</div>
                <div><strong>Suma:</strong> 0 PLN</div>
                <div><strong>Dostawcy:</strong> 0</div>
                <div><strong>Średnia:</strong> 0 PLN</div>
            </div>
        </div>
        """

    def _process_invoice(
        self,
        invoice_file,
        auto_categorize: bool,
        validate_supplier: bool,
        extract_line_items: bool,
        ocr_fallback: bool,
        customer_select: str,
        expense_category: str
    ) -> Tuple[str, Dict, str, str, go.Figure]:
        """Process uploaded invoice file."""
        try:
            if invoice_file is None:
                return (
                    """
                    <div class="status-warning">
                        <h4 style="margin: 0 0 12px 0;">⚠️ Brak Pliku</h4>
                        <p style="margin: 0;">Proszę wybrać plik faktury do przetworzenia.</p>
                    </div>
                    """,
                    {},
                    "Proszę przesłać plik faktury...",
                    "Analiza finansowa będzie dostępna po przetworzeniu...",
                    self._create_empty_financial_plot()
                )

            # Show processing status
            processing_html = """
            <div class="status-success">
                <h4 style="margin: 0 0 12px 0;">🔄 Przetwarzanie Faktury...</h4>
                <p style="margin: 0;">Analizuję dokument z wykorzystaniem AI i OCR...</p>
                <div class="progress-ring" style="margin: 12px auto;"></div>
            </div>
            """

            # Read file content
            with open(invoice_file.name, 'rb') as f:
                file_content = f.read()

            # Process document
            start_time = time.time()
            result = self.document_processor.process_document(file_content, invoice_file.name)
            processing_time = time.time() - start_time

            if not result.get("success"):
                return (
                    f"""
                    <div class="status-error">
                        <h4 style="margin: 0 0 12px 0;">❌ Błąd Przetwarzania</h4>
                        <p style="margin: 0;">Błąd: {result.get('error', 'Nieznany błąd')}</p>
                    </div>
                    """,
                    {},
                    "Błąd podczas przetwarzania...",
                    "Nie można przeprowadzić analizy finansowej...",
                    self._create_empty_financial_plot()
                )

            # Enhance results with additional processing
            enhanced_result = InvoiceProcessingHelpers.enhance_invoice_results(result, auto_categorize, validate_supplier)

            # Generate status
            success_status = f"""
            <div class="status-success">
                <h4 style="margin: 0 0 12px 0;">✅ Faktura Przetworzona</h4>
                <p style="margin: 0;"><strong>Plik:</strong> {invoice_file.name}</p>
                <p style="margin: 8px 0 0 0;"><strong>Typ dokumentu:</strong> {result.get('document_type', 'nieznany')}</p>
                <p style="margin: 8px 0 0 0;"><strong>Czas przetwarzania:</strong> {processing_time:.2f}s</p>
                <p style="margin: 8px 0 0 0;"><strong>Pewność:</strong> {result.get('confidence_score', 0)*100:.0f}%</p>
            </div>
            """

            # Generate supplier info
            supplier_html = InvoiceProcessingHelpers.generate_supplier_info(enhanced_result)

            # Generate financial analysis
            financial_html = InvoiceProcessingHelpers.generate_financial_analysis(enhanced_result, customer_select, expense_category)

            # Create financial chart
            financial_chart = InvoiceProcessingHelpers.create_financial_chart(enhanced_result)

            return success_status, enhanced_result, supplier_html, financial_html, financial_chart

        except Exception as e:
            logger.error(f"Invoice processing error: {e}")
            return (
                f"""
                <div class="status-error">
                    <h4 style="margin: 0 0 12px 0;">❌ Błąd Systemu</h4>
                    <p style="margin: 0;">Wystąpił błąd: {str(e)}</p>
                </div>
                """,
                {},
                "Błąd systemu...",
                "Nie można przeprowadzić analizy...",
                self._create_empty_financial_plot()
            )

    def _save_invoice_to_system(self, invoice_results: Dict, customer_select: str, expense_category: str) -> Tuple[str, str, str]:
        """Save processed invoice to the system."""
        try:
            if not invoice_results or not invoice_results.get("success"):
                return (
                    """
                    <div class="status-warning">
                        <h4 style="margin: 0 0 12px 0;">⚠️ Brak Danych do Zapisania</h4>
                        <p style="margin: 0;">Proszę najpierw przetworzyć fakturę.</p>
                    </div>
                    """,
                    self._get_invoice_history(),
                    self._get_monthly_stats()
                )

            # Simulate saving to database
            invoice_record = {
                "id": f"INV_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "filename": invoice_results.get("filename", "unknown"),
                "document_type": invoice_results.get("document_type", "unknown"),
                "processed_at": datetime.now().isoformat(),
                "customer": customer_select,
                "category": expense_category,
                "financial_summary": invoice_results.get("financial_summary", {}),
                "supplier_info": invoice_results.get("supplier_info", {}),
                "invoice_data": invoice_results.get("invoice_data", {})
            }

            # Add to processed invoices
            self.processed_invoices.append(invoice_record)

            # Update financial data
            self._update_financial_data(invoice_record)

            # Success status
            success_status = f"""
            <div class="status-success">
                <h4 style="margin: 0 0 12px 0;">✅ Faktura Zapisana</h4>
                <p style="margin: 0;"><strong>ID:</strong> {invoice_record['id']}</p>
                <p style="margin: 8px 0 0 0;"><strong>Klient:</strong> {customer_select or 'Nie przypisano'}</p>
                <p style="margin: 8px 0 0 0;"><strong>Kategoria:</strong> {expense_category}</p>
                <div style="margin-top: 12px; padding: 8px; background: rgba(76, 175, 80, 0.1); border-radius: 8px;">
                    💾 <strong>Sukces:</strong> Faktura została dodana do systemu CRM
                </div>
            </div>
            """

            return success_status, self._get_invoice_history(), self._get_monthly_stats()

        except Exception as e:
            logger.error(f"Invoice saving error: {e}")
            return (
                f"""
                <div class="status-error">
                    <h4 style="margin: 0 0 12px 0;">❌ Błąd Zapisywania</h4>
                    <p style="margin: 0;">Wystąpił błąd: {str(e)}</p>
                </div>
                """,
                self._get_invoice_history(),
                self._get_monthly_stats()
            )

    def _update_financial_data(self, invoice_record: Dict):
        """Update financial tracking data."""
        financial_summary = invoice_record.get("financial_summary", {})
        total_amount = financial_summary.get("total_amount", 0)
        category = invoice_record.get("category", "Inne")
        customer = invoice_record.get("customer", "Nieprzypisany")
        supplier_info = invoice_record.get("supplier_info", {})
        supplier_name = supplier_info.get("supplier_name", "Nieznany")

        # Update monthly expenses
        current_month = datetime.now().strftime('%Y-%m')
        if current_month not in self.financial_data["monthly_expenses"]:
            self.financial_data["monthly_expenses"][current_month] = 0
        self.financial_data["monthly_expenses"][current_month] += total_amount

        # Update supplier totals
        if supplier_name not in self.financial_data["supplier_totals"]:
            self.financial_data["supplier_totals"][supplier_name] = 0
        self.financial_data["supplier_totals"][supplier_name] += total_amount

        # Update category breakdown
        if category not in self.financial_data["category_breakdown"]:
            self.financial_data["category_breakdown"][category] = 0
        self.financial_data["category_breakdown"][category] += total_amount

        # Update customer costs
        if customer and customer != "Nieprzypisany":
            if customer not in self.financial_data["customer_costs"]:
                self.financial_data["customer_costs"][customer] = 0
            self.financial_data["customer_costs"][customer] += total_amount

        # Update total
        self.financial_data["total_processed"] += 1
