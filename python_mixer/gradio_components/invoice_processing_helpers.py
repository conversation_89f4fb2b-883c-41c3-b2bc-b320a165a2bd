"""
Invoice Processing Helper Methods for Fulmark.pl HVAC CRM
=========================================================

Helper methods for invoice processing component including financial analysis,
supplier information generation, and data visualization.
"""

import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
from loguru import logger


class InvoiceProcessingHelpers:
    """Helper methods for invoice processing operations."""
    
    @staticmethod
    def enhance_invoice_results(result: Dict, auto_categorize: bool, validate_supplier: bool) -> Dict:
        """Enhance invoice processing results with additional analysis."""
        enhanced = result.copy()
        
        # Add processing metadata
        enhanced["processing_options"] = {
            "auto_categorize": auto_categorize,
            "validate_supplier": validate_supplier,
            "enhanced_at": datetime.now().isoformat()
        }
        
        # Enhanced categorization
        if auto_categorize and result.get("document_type") == "invoice":
            category = InvoiceProcessingHelpers.auto_categorize_invoice(result)
            enhanced["auto_category"] = category
        
        # Enhanced supplier validation
        if validate_supplier and result.get("supplier_info", {}).get("identified"):
            validation = InvoiceProcessingHelpers.validate_supplier_details(result["supplier_info"])
            enhanced["supplier_validation"] = validation
        
        # Financial calculations
        financial_summary = InvoiceProcessingHelpers.calculate_financial_summary(result)
        enhanced["financial_summary"] = financial_summary
        
        return enhanced
    
    @staticmethod
    def auto_categorize_invoice(result: Dict) -> Dict:
        """Automatically categorize invoice based on content and supplier."""
        invoice_data = result.get("invoice_data", {})
        supplier_info = result.get("supplier_info", {})
        text = result.get("extracted_text", "").lower()
        
        category_info = {
            "category": "🔧 Sprzęt HVAC",
            "confidence": 0.5,
            "reasoning": []
        }
        
        # Category based on supplier
        if supplier_info.get("identified"):
            supplier_category = supplier_info.get("category", "")
            if supplier_category == "equipment_manufacturer":
                category_info["category"] = "🔧 Sprzęt HVAC"
                category_info["confidence"] += 0.3
                category_info["reasoning"].append("Dostawca sprzętu HVAC")
            elif supplier_category == "parts_supplier":
                category_info["category"] = "🔩 Części zamienne"
                category_info["confidence"] += 0.3
                category_info["reasoning"].append("Dostawca części zamiennych")
            elif supplier_category == "service_provider":
                category_info["category"] = "⚡ Serwis i naprawy"
                category_info["confidence"] += 0.3
                category_info["reasoning"].append("Usługodawca serwisowy")
        
        # Category based on content keywords
        if any(word in text for word in ["klimatyzator", "pompa ciepła", "jednostka", "lg", "daikin"]):
            if category_info["category"] == "🔧 Sprzęt HVAC":
                category_info["confidence"] += 0.2
            category_info["reasoning"].append("Słowa kluczowe: sprzęt HVAC")
        
        if any(word in text for word in ["filtr", "części", "komponenty", "zamienne"]):
            category_info["category"] = "🔩 Części zamienne"
            category_info["confidence"] += 0.2
            category_info["reasoning"].append("Słowa kluczowe: części zamienne")
        
        if any(word in text for word in ["serwis", "naprawa", "konserwacja", "przegląd"]):
            category_info["category"] = "⚡ Serwis i naprawy"
            category_info["confidence"] += 0.2
            category_info["reasoning"].append("Słowa kluczowe: serwis")
        
        if any(word in text for word in ["prąd", "energia", "media", "administracja"]):
            category_info["category"] = "🏢 Media i administracja"
            category_info["confidence"] += 0.2
            category_info["reasoning"].append("Słowa kluczowe: media")
        
        # Normalize confidence
        category_info["confidence"] = min(category_info["confidence"], 1.0)
        
        return category_info
    
    @staticmethod
    def validate_supplier_details(supplier_info: Dict) -> Dict:
        """Validate supplier details against database."""
        validation = {
            "is_valid": True,
            "warnings": [],
            "recommendations": []
        }
        
        # Check if supplier is in good standing
        supplier_id = supplier_info.get("supplier_id")
        if supplier_id in ["lg", "daikin"]:
            validation["recommendations"].append("Autoryzowany partner - sprawdź warunki gwarancji")
        
        # Check confidence level
        confidence = supplier_info.get("confidence", 0)
        if confidence < 0.8:
            validation["warnings"].append(f"Niska pewność identyfikacji dostawcy ({confidence*100:.0f}%)")
        
        # Check match method
        match_method = supplier_info.get("match_method")
        if match_method == "name_similarity":
            validation["warnings"].append("Identyfikacja na podstawie nazwy - sprawdź NIP")
        
        return validation
    
    @staticmethod
    def calculate_financial_summary(result: Dict) -> Dict:
        """Calculate financial summary from invoice data."""
        invoice_data = result.get("invoice_data", {})
        
        summary = {
            "total_amount": invoice_data.get("total_amount", 0),
            "currency": invoice_data.get("currency", "PLN"),
            "line_items_count": len(invoice_data.get("line_items", [])),
            "average_item_value": 0,
            "tax_amount": 0,
            "net_amount": 0
        }
        
        # Calculate averages and tax estimates
        total = summary["total_amount"] or 0
        items_count = summary["line_items_count"]
        
        if items_count > 0 and total > 0:
            summary["average_item_value"] = total / items_count
        
        # Estimate tax (assuming 23% VAT for Poland)
        if total > 0:
            summary["net_amount"] = total / 1.23
            summary["tax_amount"] = total - summary["net_amount"]
        
        return summary
    
    @staticmethod
    def generate_supplier_info(result: Dict) -> str:
        """Generate supplier information HTML."""
        supplier_info = result.get("supplier_info", {})
        supplier_validation = result.get("supplier_validation", {})
        
        if not supplier_info.get("identified"):
            return """
            <div class="metric-card">
                <h4 style="margin: 0 0 12px 0; color: #ff9800;">🏭 Dostawca Nierozpoznany</h4>
                <p style="margin: 0; color: #666;">
                    Nie udało się zidentyfikować dostawcy w bazie danych HVAC.
                </p>
                <div style="margin-top: 12px; padding: 8px; background: #fff3e0; border-radius: 8px;">
                    💡 <strong>Sugestia:</strong> Sprawdź ręcznie dane dostawcy i dodaj do bazy
                </div>
            </div>
            """
        
        supplier_name = supplier_info.get("supplier_name", "Nieznany")
        supplier_category = supplier_info.get("category", "nieznana")
        confidence = supplier_info.get("confidence", 0) * 100
        match_method = supplier_info.get("match_method", "nieznana")
        
        # Category emoji and description
        category_map = {
            "equipment_manufacturer": ("🏭", "Producent sprzętu HVAC"),
            "parts_supplier": ("🔩", "Dostawca części zamiennych"),
            "service_provider": ("⚡", "Usługodawca serwisowy"),
            "unknown": ("❓", "Nieznana kategoria")
        }
        
        category_emoji, category_desc = category_map.get(supplier_category, ("❓", "Nieznana kategoria"))
        
        # Confidence color
        if confidence >= 90:
            confidence_color = "#4caf50"
            confidence_text = "Bardzo wysoka"
        elif confidence >= 70:
            confidence_color = "#ff9800"
            confidence_text = "Wysoka"
        elif confidence >= 50:
            confidence_color = "#ff9800"
            confidence_text = "Średnia"
        else:
            confidence_color = "#f44336"
            confidence_text = "Niska"
        
        html = f"""
        <div class="metric-card">
            <h4 style="margin: 0 0 16px 0; color: #1a73e8;">🏭 Informacje o Dostawcy</h4>
            
            <div style="margin-bottom: 16px;">
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <span style="font-size: 1.5em; margin-right: 8px;">{category_emoji}</span>
                    <div>
                        <strong>{supplier_name}</strong><br>
                        <span style="color: #666; font-size: 0.9em;">{category_desc}</span>
                    </div>
                </div>
                
                <div style="margin-top: 12px; padding: 8px; background: #f8f9fa; border-radius: 8px; font-size: 0.9em;">
                    <strong>Pewność identyfikacji:</strong> 
                    <span style="color: {confidence_color}; font-weight: 600;">{confidence:.0f}% ({confidence_text})</span><br>
                    <strong>Metoda dopasowania:</strong> {match_method}
                </div>
            </div>
        """
        
        # Add validation warnings and recommendations
        if supplier_validation:
            warnings = supplier_validation.get("warnings", [])
            recommendations = supplier_validation.get("recommendations", [])
            
            if warnings:
                html += """
                <div style="margin-top: 12px; padding: 8px; background: #fff3e0; border-radius: 8px; border-left: 4px solid #ff9800;">
                    <strong>⚠️ Ostrzeżenia:</strong><br>
                """
                for warning in warnings:
                    html += f"• {warning}<br>"
                html += "</div>"
            
            if recommendations:
                html += """
                <div style="margin-top: 12px; padding: 8px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #4caf50;">
                    <strong>💡 Rekomendacje:</strong><br>
                """
                for rec in recommendations:
                    html += f"• {rec}<br>"
                html += "</div>"
        
        html += "</div>"
        return html
    
    @staticmethod
    def generate_financial_analysis(result: Dict, customer_select: str, expense_category: str) -> str:
        """Generate financial analysis HTML."""
        financial_summary = result.get("financial_summary", {})
        invoice_data = result.get("invoice_data", {})
        auto_category = result.get("auto_category", {})
        
        total_amount = financial_summary.get("total_amount", 0)
        currency = financial_summary.get("currency", "PLN")
        net_amount = financial_summary.get("net_amount", 0)
        tax_amount = financial_summary.get("tax_amount", 0)
        line_items_count = financial_summary.get("line_items_count", 0)
        
        html = f"""
        <div class="metric-card">
            <h4 style="margin: 0 0 16px 0; color: #1a73e8;">💰 Analiza Finansowa</h4>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 16px;">
                
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); border-radius: 12px;">
                    <div style="font-size: 1.8em; font-weight: 700; color: #1976d2;">{total_amount:.2f} {currency}</div>
                    <div style="font-size: 0.9em; color: #424242;">Kwota Brutto</div>
                </div>
                
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%); border-radius: 12px;">
                    <div style="font-size: 1.8em; font-weight: 700; color: #388e3c;">{net_amount:.2f} {currency}</div>
                    <div style="font-size: 0.9em; color: #424242;">Kwota Netto</div>
                </div>
                
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%); border-radius: 12px;">
                    <div style="font-size: 1.8em; font-weight: 700; color: #f57c00;">{tax_amount:.2f} {currency}</div>
                    <div style="font-size: 0.9em; color: #424242;">VAT (23%)</div>
                </div>
                
                <div style="text-align: center; padding: 12px; background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%); border-radius: 12px;">
                    <div style="font-size: 1.8em; font-weight: 700; color: #7b1fa2;">{line_items_count}</div>
                    <div style="font-size: 0.9em; color: #424242;">Pozycji</div>
                </div>
                
            </div>
        """
        
        # Add categorization info
        if auto_category:
            category = auto_category.get("category", expense_category)
            confidence = auto_category.get("confidence", 0) * 100
            reasoning = auto_category.get("reasoning", [])
            
            html += f"""
            <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <strong>🏷️ Kategoryzacja Automatyczna:</strong><br>
                <span style="font-size: 1.1em; font-weight: 600;">{category}</span> 
                <span style="color: #4caf50;">({confidence:.0f}% pewności)</span><br>
                <div style="margin-top: 8px; font-size: 0.9em; color: #666;">
                    <strong>Uzasadnienie:</strong> {' • '.join(reasoning)}
                </div>
            </div>
            """
        
        # Add customer assignment info
        if customer_select:
            html += f"""
            <div style="margin-top: 12px; padding: 12px; background: #e3f2fd; border-radius: 8px;">
                <strong>👤 Przypisanie do klienta:</strong><br>
                {customer_select}
            </div>
            """
        
        # Add invoice details
        invoice_number = invoice_data.get("invoice_number")
        invoice_date = invoice_data.get("date")
        vendor_name = invoice_data.get("vendor_name")
        
        if any([invoice_number, invoice_date, vendor_name]):
            html += """
            <div style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 8px;">
                <strong>📋 Szczegóły Faktury:</strong><br>
            """
            if invoice_number:
                html += f"<strong>Numer:</strong> {invoice_number}<br>"
            if invoice_date:
                html += f"<strong>Data:</strong> {invoice_date}<br>"
            if vendor_name:
                html += f"<strong>Dostawca:</strong> {vendor_name}<br>"
            html += "</div>"
        
        html += "</div>"
        return html
    
    @staticmethod
    def create_financial_chart(result: Dict) -> go.Figure:
        """Create financial visualization chart."""
        financial_summary = result.get("financial_summary", {})
        invoice_data = result.get("invoice_data", {})
        
        total_amount = financial_summary.get("total_amount", 0)
        net_amount = financial_summary.get("net_amount", 0)
        tax_amount = financial_summary.get("tax_amount", 0)
        
        # Create breakdown chart
        fig = go.Figure()
        
        if total_amount > 0:
            # Add breakdown bars
            fig.add_trace(go.Bar(
                x=['Kwota Netto', 'VAT 23%', 'Razem Brutto'],
                y=[net_amount, tax_amount, total_amount],
                marker_color=['#4caf50', '#ff9800', '#1a73e8'],
                text=[f'{net_amount:.2f} PLN', f'{tax_amount:.2f} PLN', f'{total_amount:.2f} PLN'],
                textposition='auto'
            ))
            
            title = f"💰 Analiza Finansowa Faktury"
        else:
            # Empty chart
            fig.add_trace(go.Bar(
                x=['Brak danych'],
                y=[0],
                marker_color=['#e0e0e0']
            ))
            title = "💰 Analiza Finansowa - Brak Danych"
        
        fig.update_layout(
            title=title,
            xaxis_title="Kategorie",
            yaxis_title="Kwota (PLN)",
            template="plotly_white",
            height=400,
            font=dict(family="Google Sans, Roboto, sans-serif"),
            title_font=dict(size=16, color="#1a73e8"),
            plot_bgcolor='rgba(248,249,255,0.8)',
            paper_bgcolor='rgba(255,255,255,0.9)',
            showlegend=False
        )
        
        return fig
