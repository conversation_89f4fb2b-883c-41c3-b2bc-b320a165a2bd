#!/usr/bin/env python3
"""
Simple Launcher for Enhanced Human Comprehension Interface
Gradio 5.0+ with Real-time Streaming & Advanced Features
"""

import sys
import os
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """Launch the enhanced interface."""
    print("🚀 Starting Enhanced Human Comprehension Interface - Gradio 5.0+ Edition...")
    print("✨ Features: 8x faster performance, real-time streaming, ChatInterface, tool use capabilities")
    
    try:
        from enhanced_human_comprehension_interface import HumanComprehensionInterface
        
        print("🎨 Creating interface...")
        interface_manager = HumanComprehensionInterface()
        interface = interface_manager.create_interface()
        
        print("✅ Interface created successfully!")
        print("🚀 Gradio 5.0+ Enhanced Features:")
        print("   • 8x faster performance with real-time streaming")
        print("   • ChatInterface with Messages API for LLM integration")
        print("   • Tool use capabilities with expandable message boxes")
        print("   • Enhanced accessibility compliance (WCAG 2.1 AA+)")
        print("   • Server-Side Events for better scalability")
        print("   • Real-time performance monitoring")
        
        # Launch with Gradio 5.0+ settings
        print("\n🌐 Launching interface on http://localhost:7860")
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=True,
            show_error=True,
            quiet=False,
            inbrowser=True
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install gradio>=5.0.0 plotly pandas numpy loguru")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error launching interface: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
