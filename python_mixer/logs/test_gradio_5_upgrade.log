2025-05-30 12:28:38 | INFO | 
==================================================
2025-05-30 12:28:38 | INFO | Running: Gradio Import
2025-05-30 12:28:38 | INFO | ==================================================
2025-05-30 12:28:38 | INFO | 🧪 Testing Gradio 5.0+ import...
2025-05-30 12:28:40 | INFO | ✅ Gradio version: 5.31.0
2025-05-30 12:28:40 | INFO | ✅ Gradio 5.0+ successfully imported
2025-05-30 12:28:40 | INFO | ✅ Gradio Import: PASSED
2025-05-30 12:28:40 | INFO | 
==================================================
2025-05-30 12:28:40 | INFO | Running: ChatInterface Import
2025-05-30 12:28:40 | INFO | ==================================================
2025-05-30 12:28:40 | INFO | 🧪 Testing ChatInterface import...
2025-05-30 12:28:40 | INFO | ✅ ChatInterface successfully imported and created
2025-05-30 12:28:40 | INFO | ✅ ChatInterface Import: PASSED
2025-05-30 12:28:40 | INFO | 
==================================================
2025-05-30 12:28:40 | INFO | Running: Enhanced Interface Import
2025-05-30 12:28:40 | INFO | ==================================================
2025-05-30 12:28:40 | INFO | 🧪 Testing Enhanced Interface import...
2025-05-30 12:28:40 | ERROR | ❌ Failed to import Enhanced Interface: No module named 'plotly'
2025-05-30 12:28:40 | ERROR | Error details: Traceback (most recent call last):
  File "/home/<USER>/HVAC/unifikacja/python_mixer/test_gradio_5_upgrade.py", line 95, in test_enhanced_interface_import
    from enhanced_human_comprehension_interface import HumanComprehensionInterface
  File "/home/<USER>/HVAC/unifikacja/python_mixer/enhanced_human_comprehension_interface.py", line 24, in <module>
    import plotly.graph_objects as go
ModuleNotFoundError: No module named 'plotly'

2025-05-30 12:28:40 | ERROR | ❌ Enhanced Interface Import: FAILED
2025-05-30 12:28:40 | INFO | 
==================================================
2025-05-30 12:28:40 | INFO | Running: Interface Creation
2025-05-30 12:28:40 | INFO | ==================================================
2025-05-30 12:28:40 | INFO | 🧪 Testing interface creation...
2025-05-30 12:28:40 | ERROR | ❌ Failed to create interface: No module named 'plotly'
2025-05-30 12:28:40 | ERROR | Error details: Traceback (most recent call last):
  File "/home/<USER>/HVAC/unifikacja/python_mixer/test_gradio_5_upgrade.py", line 115, in test_interface_creation
    from enhanced_human_comprehension_interface import HumanComprehensionInterface
  File "/home/<USER>/HVAC/unifikacja/python_mixer/enhanced_human_comprehension_interface.py", line 24, in <module>
    import plotly.graph_objects as go
ModuleNotFoundError: No module named 'plotly'

2025-05-30 12:28:40 | ERROR | ❌ Interface Creation: FAILED
2025-05-30 12:28:40 | INFO | 
==================================================
2025-05-30 12:28:40 | INFO | Running: AI Chat Functionality
2025-05-30 12:28:40 | INFO | ==================================================
2025-05-30 12:28:40 | INFO | 🧪 Testing AI Chat Assistant...
2025-05-30 12:28:40 | ERROR | ❌ AI Chat test failed: No module named 'plotly'
2025-05-30 12:28:40 | ERROR | ❌ AI Chat Functionality: FAILED
2025-05-30 12:28:40 | INFO | 
==================================================
2025-05-30 12:28:40 | INFO | Running: Tool Use Capabilities
2025-05-30 12:28:40 | INFO | ==================================================
2025-05-30 12:28:40 | INFO | 🧪 Testing tool use capabilities...
2025-05-30 12:28:40 | ERROR | ❌ Tool use test failed: No module named 'plotly'
2025-05-30 12:28:40 | ERROR | ❌ Tool Use Capabilities: FAILED
2025-05-30 12:28:40 | INFO | 
==================================================
2025-05-30 12:28:40 | INFO | Running: Calendar Management
2025-05-30 12:28:40 | INFO | ==================================================
2025-05-30 12:28:40 | INFO | 🧪 Testing Enhanced Calendar Management...
2025-05-30 12:28:40 | ERROR | ❌ Calendar Management test failed: No module named 'plotly'
2025-05-30 12:28:40 | ERROR | ❌ Calendar Management: FAILED
2025-05-30 12:28:40 | INFO | 
============================================================
2025-05-30 12:28:40 | INFO | 🎯 GRADIO 5.0+ UPGRADE TEST REPORT
2025-05-30 12:28:40 | INFO | ============================================================
2025-05-30 12:28:40 | INFO | 📊 Overall Results: 2/7 tests passed (28.6%)
2025-05-30 12:28:40 | INFO | 
📋 Test Results:
2025-05-30 12:28:40 | INFO |   ✅ gradio_version: PASS
2025-05-30 12:28:40 | INFO |   ✅ chat_interface: PASS
2025-05-30 12:28:40 | INFO |   ❌ enhanced_interface: FAIL
2025-05-30 12:28:40 | INFO |   ❌ interface_creation: FAIL
2025-05-30 12:28:40 | INFO |   ❌ ai_chat: FAIL
2025-05-30 12:28:40 | INFO |   ❌ tool_use: FAIL
2025-05-30 12:28:40 | INFO |   ❌ calendar_management: FAIL
2025-05-30 12:28:40 | ERROR | 
❌ UPGRADE ISSUES: Significant problems detected
2025-05-30 12:28:40 | INFO | ============================================================
2025-05-30 12:29:09 | INFO | 
==================================================
2025-05-30 12:29:09 | INFO | Running: Gradio Import
2025-05-30 12:29:09 | INFO | ==================================================
2025-05-30 12:29:09 | INFO | 🧪 Testing Gradio 5.0+ import...
2025-05-30 12:29:11 | INFO | ✅ Gradio version: 5.31.0
2025-05-30 12:29:11 | INFO | ✅ Gradio 5.0+ successfully imported
2025-05-30 12:29:11 | INFO | ✅ Gradio Import: PASSED
2025-05-30 12:29:11 | INFO | 
==================================================
2025-05-30 12:29:11 | INFO | Running: ChatInterface Import
2025-05-30 12:29:11 | INFO | ==================================================
2025-05-30 12:29:11 | INFO | 🧪 Testing ChatInterface import...
2025-05-30 12:29:11 | INFO | ✅ ChatInterface successfully imported and created
2025-05-30 12:29:11 | INFO | ✅ ChatInterface Import: PASSED
2025-05-30 12:29:11 | INFO | 
==================================================
2025-05-30 12:29:11 | INFO | Running: Enhanced Interface Import
2025-05-30 12:29:11 | INFO | ==================================================
2025-05-30 12:29:11 | INFO | 🧪 Testing Enhanced Interface import...
2025-05-30 12:29:11 | ERROR | ❌ Failed to import Enhanced Interface: attempted relative import with no known parent package
2025-05-30 12:29:11 | ERROR | Error details: Traceback (most recent call last):
  File "/home/<USER>/HVAC/unifikacja/python_mixer/test_gradio_5_upgrade.py", line 95, in test_enhanced_interface_import
    from enhanced_human_comprehension_interface import HumanComprehensionInterface
  File "/home/<USER>/HVAC/unifikacja/python_mixer/enhanced_human_comprehension_interface.py", line 38, in <module>
    from .main import HVACEmailAnalysisSystem
ImportError: attempted relative import with no known parent package

2025-05-30 12:29:11 | ERROR | ❌ Enhanced Interface Import: FAILED
2025-05-30 12:29:11 | INFO | 
==================================================
2025-05-30 12:29:11 | INFO | Running: Interface Creation
2025-05-30 12:29:11 | INFO | ==================================================
2025-05-30 12:29:11 | INFO | 🧪 Testing interface creation...
2025-05-30 12:29:11 | ERROR | ❌ Failed to create interface: attempted relative import with no known parent package
2025-05-30 12:29:11 | ERROR | Error details: Traceback (most recent call last):
  File "/home/<USER>/HVAC/unifikacja/python_mixer/test_gradio_5_upgrade.py", line 115, in test_interface_creation
    from enhanced_human_comprehension_interface import HumanComprehensionInterface
  File "/home/<USER>/HVAC/unifikacja/python_mixer/enhanced_human_comprehension_interface.py", line 38, in <module>
    from .main import HVACEmailAnalysisSystem
ImportError: attempted relative import with no known parent package

2025-05-30 12:29:11 | ERROR | ❌ Interface Creation: FAILED
2025-05-30 12:29:11 | INFO | 
==================================================
2025-05-30 12:29:11 | INFO | Running: AI Chat Functionality
2025-05-30 12:29:11 | INFO | ==================================================
2025-05-30 12:29:11 | INFO | 🧪 Testing AI Chat Assistant...
2025-05-30 12:29:11 | ERROR | ❌ AI Chat test failed: attempted relative import with no known parent package
2025-05-30 12:29:11 | ERROR | ❌ AI Chat Functionality: FAILED
2025-05-30 12:29:11 | INFO | 
==================================================
2025-05-30 12:29:11 | INFO | Running: Tool Use Capabilities
2025-05-30 12:29:11 | INFO | ==================================================
2025-05-30 12:29:11 | INFO | 🧪 Testing tool use capabilities...
2025-05-30 12:29:11 | ERROR | ❌ Tool use test failed: attempted relative import with no known parent package
2025-05-30 12:29:11 | ERROR | ❌ Tool Use Capabilities: FAILED
2025-05-30 12:29:11 | INFO | 
==================================================
2025-05-30 12:29:11 | INFO | Running: Calendar Management
2025-05-30 12:29:11 | INFO | ==================================================
2025-05-30 12:29:11 | INFO | 🧪 Testing Enhanced Calendar Management...
2025-05-30 12:29:11 | ERROR | ❌ Calendar Management test failed: attempted relative import with no known parent package
2025-05-30 12:29:11 | ERROR | ❌ Calendar Management: FAILED
2025-05-30 12:29:11 | INFO | 
============================================================
2025-05-30 12:29:11 | INFO | 🎯 GRADIO 5.0+ UPGRADE TEST REPORT
2025-05-30 12:29:11 | INFO | ============================================================
2025-05-30 12:29:11 | INFO | 📊 Overall Results: 2/7 tests passed (28.6%)
2025-05-30 12:29:11 | INFO | 
📋 Test Results:
2025-05-30 12:29:11 | INFO |   ✅ gradio_version: PASS
2025-05-30 12:29:11 | INFO |   ✅ chat_interface: PASS
2025-05-30 12:29:11 | INFO |   ❌ enhanced_interface: FAIL
2025-05-30 12:29:11 | INFO |   ❌ interface_creation: FAIL
2025-05-30 12:29:11 | INFO |   ❌ ai_chat: FAIL
2025-05-30 12:29:11 | INFO |   ❌ tool_use: FAIL
2025-05-30 12:29:11 | INFO |   ❌ calendar_management: FAIL
2025-05-30 12:29:11 | ERROR | 
❌ UPGRADE ISSUES: Significant problems detected
2025-05-30 12:29:11 | INFO | ============================================================
2025-05-30 12:30:39 | INFO | 
==================================================
2025-05-30 12:30:39 | INFO | Running: Gradio Import
2025-05-30 12:30:39 | INFO | ==================================================
2025-05-30 12:30:39 | INFO | 🧪 Testing Gradio 5.0+ import...
2025-05-30 12:30:41 | INFO | ✅ Gradio version: 5.31.0
2025-05-30 12:30:41 | INFO | ✅ Gradio 5.0+ successfully imported
2025-05-30 12:30:41 | INFO | ✅ Gradio Import: PASSED
2025-05-30 12:30:41 | INFO | 
==================================================
2025-05-30 12:30:41 | INFO | Running: ChatInterface Import
2025-05-30 12:30:41 | INFO | ==================================================
2025-05-30 12:30:41 | INFO | 🧪 Testing ChatInterface import...
2025-05-30 12:30:41 | INFO | ✅ ChatInterface successfully imported and created
2025-05-30 12:30:41 | INFO | ✅ ChatInterface Import: PASSED
2025-05-30 12:30:41 | INFO | 
==================================================
2025-05-30 12:30:41 | INFO | Running: Enhanced Interface Import
2025-05-30 12:30:41 | INFO | ==================================================
2025-05-30 12:30:41 | INFO | 🧪 Testing Enhanced Interface import...
2025-05-30 12:30:41 | WARNING | Krabulon agents not available
2025-05-30 12:30:41 | WARNING | Database models not available
2025-05-30 12:30:41 | WARNING | Database models not available - running in standalone mode
2025-05-30 12:30:41 | WARNING | Calendar agents not available
2025-05-30 12:30:41 | WARNING | Advanced Calendar Management Agent not available
2025-05-30 12:30:41 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:30:41 | INFO | ✅ Enhanced Interface successfully imported
2025-05-30 12:30:41 | INFO | ✅ Enhanced Interface Import: PASSED
2025-05-30 12:30:41 | INFO | 
==================================================
2025-05-30 12:30:41 | INFO | Running: Interface Creation
2025-05-30 12:30:41 | INFO | ==================================================
2025-05-30 12:30:41 | INFO | 🧪 Testing interface creation...
2025-05-30 12:30:41 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:30:41 | ERROR | ❌ Failed to create interface: ChatInterface.__init__() got an unexpected keyword argument 'retry_btn'
2025-05-30 12:30:41 | ERROR | Error details: Traceback (most recent call last):
  File "/home/<USER>/HVAC/unifikacja/python_mixer/test_gradio_5_upgrade.py", line 119, in test_interface_creation
    interface = interface_manager.create_interface()
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/HVAC/unifikacja/python_mixer/enhanced_human_comprehension_interface.py", line 510, in create_interface
    self._create_ai_chat_interface()
  File "/home/<USER>/HVAC/unifikacja/python_mixer/enhanced_human_comprehension_interface.py", line 544, in _create_ai_chat_interface
    chat_interface = ChatInterface(
                     ^^^^^^^^^^^^^^
TypeError: ChatInterface.__init__() got an unexpected keyword argument 'retry_btn'

2025-05-30 12:30:41 | ERROR | ❌ Interface Creation: FAILED
2025-05-30 12:30:41 | INFO | 
==================================================
2025-05-30 12:30:41 | INFO | Running: AI Chat Functionality
2025-05-30 12:30:41 | INFO | ==================================================
2025-05-30 12:30:41 | INFO | 🧪 Testing AI Chat Assistant...
2025-05-30 12:30:41 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:30:41 | INFO | ✅ AI Chat responded in 0.00s
2025-05-30 12:30:41 | INFO | Response preview: I'm your HVAC AI assistant! I can help you with:

🔧 **Technical Support:**
- Equipment troubleshooti...
2025-05-30 12:30:41 | INFO | ✅ AI Chat Functionality: PASSED
2025-05-30 12:30:41 | INFO | 
==================================================
2025-05-30 12:30:41 | INFO | Running: Tool Use Capabilities
2025-05-30 12:30:41 | INFO | ==================================================
2025-05-30 12:30:41 | INFO | 🧪 Testing tool use capabilities...
2025-05-30 12:30:41 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:30:41 | INFO | ✅ Tool use detected: 📧 Email Analysis Tool
2025-05-30 12:30:41 | INFO | ✅ Tool Use Capabilities: PASSED
2025-05-30 12:30:41 | INFO | 
==================================================
2025-05-30 12:30:41 | INFO | Running: Calendar Management
2025-05-30 12:30:41 | INFO | ==================================================
2025-05-30 12:30:41 | INFO | 🧪 Testing Enhanced Calendar Management...
2025-05-30 12:30:41 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:30:41 | INFO | ✅ Calendar Management working
2025-05-30 12:30:41 | INFO | ✅ Calendar Management: PASSED
2025-05-30 12:30:41 | INFO | 
============================================================
2025-05-30 12:30:41 | INFO | 🎯 GRADIO 5.0+ UPGRADE TEST REPORT
2025-05-30 12:30:41 | INFO | ============================================================
2025-05-30 12:30:41 | INFO | 📊 Overall Results: 6/7 tests passed (85.7%)
2025-05-30 12:30:41 | INFO | 
📋 Test Results:
2025-05-30 12:30:41 | INFO |   ✅ gradio_version: PASS
2025-05-30 12:30:41 | INFO |   ✅ chat_interface: PASS
2025-05-30 12:30:41 | INFO |   ✅ enhanced_interface: PASS
2025-05-30 12:30:41 | INFO |   ❌ interface_creation: FAIL
2025-05-30 12:30:41 | INFO |   ✅ ai_chat: PASS
2025-05-30 12:30:41 | INFO |   ✅ tool_use: PASS
2025-05-30 12:30:41 | INFO |   ✅ calendar_management: PASS
2025-05-30 12:30:41 | INFO | 
⚡ Performance Metrics:
2025-05-30 12:30:41 | INFO |   📈 ai_response_time: 0.000s
2025-05-30 12:30:41 | INFO | 
🎉 UPGRADE SUCCESS: Gradio 5.0+ upgrade is working well!
2025-05-30 12:30:41 | INFO | ============================================================
2025-05-30 12:31:13 | INFO | 
==================================================
2025-05-30 12:31:13 | INFO | Running: Gradio Import
2025-05-30 12:31:13 | INFO | ==================================================
2025-05-30 12:31:13 | INFO | 🧪 Testing Gradio 5.0+ import...
2025-05-30 12:31:14 | INFO | ✅ Gradio version: 5.31.0
2025-05-30 12:31:14 | INFO | ✅ Gradio 5.0+ successfully imported
2025-05-30 12:31:14 | INFO | ✅ Gradio Import: PASSED
2025-05-30 12:31:14 | INFO | 
==================================================
2025-05-30 12:31:14 | INFO | Running: ChatInterface Import
2025-05-30 12:31:14 | INFO | ==================================================
2025-05-30 12:31:14 | INFO | 🧪 Testing ChatInterface import...
2025-05-30 12:31:14 | INFO | ✅ ChatInterface successfully imported and created
2025-05-30 12:31:14 | INFO | ✅ ChatInterface Import: PASSED
2025-05-30 12:31:14 | INFO | 
==================================================
2025-05-30 12:31:14 | INFO | Running: Enhanced Interface Import
2025-05-30 12:31:14 | INFO | ==================================================
2025-05-30 12:31:14 | INFO | 🧪 Testing Enhanced Interface import...
2025-05-30 12:31:15 | WARNING | Krabulon agents not available
2025-05-30 12:31:15 | WARNING | Database models not available
2025-05-30 12:31:15 | WARNING | Database models not available - running in standalone mode
2025-05-30 12:31:15 | WARNING | Calendar agents not available
2025-05-30 12:31:15 | WARNING | Advanced Calendar Management Agent not available
2025-05-30 12:31:15 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:31:15 | INFO | ✅ Enhanced Interface successfully imported
2025-05-30 12:31:15 | INFO | ✅ Enhanced Interface Import: PASSED
2025-05-30 12:31:15 | INFO | 
==================================================
2025-05-30 12:31:15 | INFO | Running: Interface Creation
2025-05-30 12:31:15 | INFO | ==================================================
2025-05-30 12:31:15 | INFO | 🧪 Testing interface creation...
2025-05-30 12:31:15 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:31:15 | INFO | ✅ Interface created successfully in 0.27s
2025-05-30 12:31:15 | INFO | ✅ Interface Creation: PASSED
2025-05-30 12:31:15 | INFO | 
==================================================
2025-05-30 12:31:15 | INFO | Running: AI Chat Functionality
2025-05-30 12:31:15 | INFO | ==================================================
2025-05-30 12:31:15 | INFO | 🧪 Testing AI Chat Assistant...
2025-05-30 12:31:15 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:31:15 | INFO | ✅ AI Chat responded in 0.00s
2025-05-30 12:31:15 | INFO | Response preview: I'm your HVAC AI assistant! I can help you with:

🔧 **Technical Support:**
- Equipment troubleshooti...
2025-05-30 12:31:15 | INFO | ✅ AI Chat Functionality: PASSED
2025-05-30 12:31:15 | INFO | 
==================================================
2025-05-30 12:31:15 | INFO | Running: Tool Use Capabilities
2025-05-30 12:31:15 | INFO | ==================================================
2025-05-30 12:31:15 | INFO | 🧪 Testing tool use capabilities...
2025-05-30 12:31:15 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:31:15 | INFO | ✅ Tool use detected: 📧 Email Analysis Tool
2025-05-30 12:31:15 | INFO | ✅ Tool Use Capabilities: PASSED
2025-05-30 12:31:15 | INFO | 
==================================================
2025-05-30 12:31:15 | INFO | Running: Calendar Management
2025-05-30 12:31:15 | INFO | ==================================================
2025-05-30 12:31:15 | INFO | 🧪 Testing Enhanced Calendar Management...
2025-05-30 12:31:15 | INFO | Enhanced Human Comprehension Interface initialized successfully
2025-05-30 12:31:15 | INFO | ✅ Calendar Management working
2025-05-30 12:31:15 | INFO | ✅ Calendar Management: PASSED
2025-05-30 12:31:15 | INFO | 
============================================================
2025-05-30 12:31:15 | INFO | 🎯 GRADIO 5.0+ UPGRADE TEST REPORT
2025-05-30 12:31:15 | INFO | ============================================================
2025-05-30 12:31:15 | INFO | 📊 Overall Results: 7/7 tests passed (100.0%)
2025-05-30 12:31:15 | INFO | 
📋 Test Results:
2025-05-30 12:31:15 | INFO |   ✅ gradio_version: PASS
2025-05-30 12:31:15 | INFO |   ✅ chat_interface: PASS
2025-05-30 12:31:15 | INFO |   ✅ enhanced_interface: PASS
2025-05-30 12:31:15 | INFO |   ✅ interface_creation: PASS
2025-05-30 12:31:15 | INFO |   ✅ ai_chat: PASS
2025-05-30 12:31:15 | INFO |   ✅ tool_use: PASS
2025-05-30 12:31:15 | INFO |   ✅ calendar_management: PASS
2025-05-30 12:31:15 | INFO | 
⚡ Performance Metrics:
2025-05-30 12:31:15 | INFO |   📈 interface_creation_time: 0.270s
2025-05-30 12:31:15 | INFO |   📈 ai_response_time: 0.000s
2025-05-30 12:31:15 | INFO | 
🎉 UPGRADE SUCCESS: Gradio 5.0+ upgrade is working well!
2025-05-30 12:31:15 | INFO | ============================================================
