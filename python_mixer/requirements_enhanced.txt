# Enhanced Requirements for Fulmark.pl HVAC CRM Interface
# ========================================================
# 
# Advanced document processing and invoice analysis capabilities
# with PDF processing, OCR, and AI-powered data extraction.

# Core Gradio and UI
gradio>=5.0.0
plotly>=5.15.0
pandas>=2.0.0
numpy>=1.24.0

# Logging and utilities
loguru>=0.7.0

# PDF Processing Libraries
PyPDF2>=3.0.1
pdfplumber>=0.9.0

# OCR and Image Processing
pytesseract>=0.3.10
Pillow>=10.0.0
pdf2image>=1.16.3

# Additional data processing
python-dateutil>=2.8.2
regex>=2023.8.8

# Optional: Advanced AI/ML libraries (if needed)
# transformers>=4.30.0
# torch>=2.0.0
# sentence-transformers>=2.2.2

# Development and testing
pytest>=7.4.0
pytest-cov>=4.1.0

# Database connectivity (for future integration)
psycopg2-binary>=2.9.7
sqlalchemy>=2.0.0

# File handling and utilities
pathlib2>=2.3.7
typing-extensions>=4.7.0

# Web scraping and data extraction (for future enhancements)
requests>=2.31.0
beautifulsoup4>=4.12.0

# Configuration and environment
python-dotenv>=1.0.0
pydantic>=2.0.0

# Performance and caching
redis>=4.6.0
celery>=5.3.0

# Security and encryption
cryptography>=41.0.0
bcrypt>=4.0.0

# API and web services
fastapi>=0.100.0
uvicorn>=0.23.0

# Monitoring and metrics
prometheus-client>=0.17.0

# Documentation
mkdocs>=1.5.0
mkdocs-material>=9.1.0

# Code quality
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# Installation Notes:
# ==================
# 
# 1. PDF Processing:
#    - Requires poppler-utils for pdf2image
#    - Ubuntu/Debian: sudo apt-get install poppler-utils
#    - macOS: brew install poppler
#    - Windows: Download from https://poppler.freedesktop.org/
# 
# 2. OCR (Tesseract):
#    - Requires Tesseract OCR engine
#    - Ubuntu/Debian: sudo apt-get install tesseract-ocr tesseract-ocr-pol
#    - macOS: brew install tesseract tesseract-lang
#    - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# 
# 3. Polish Language Support:
#    - Install Polish language pack for Tesseract
#    - tesseract-ocr-pol package for Polish text recognition
# 
# 4. System Dependencies:
#    - libpoppler-cpp-dev (for pdfplumber)
#    - libtesseract-dev (for pytesseract)
#    - libmagic1 (for file type detection)
# 
# Quick Installation Commands:
# ===========================
# 
# Ubuntu/Debian:
# sudo apt-get update
# sudo apt-get install poppler-utils tesseract-ocr tesseract-ocr-pol
# sudo apt-get install libpoppler-cpp-dev libtesseract-dev libmagic1
# pip install -r requirements_enhanced.txt
# 
# macOS:
# brew install poppler tesseract tesseract-lang
# pip install -r requirements_enhanced.txt
# 
# Windows:
# 1. Download and install Poppler from https://poppler.freedesktop.org/
# 2. Download and install Tesseract from https://github.com/UB-Mannheim/tesseract/wiki
# 3. Add both to system PATH
# 4. pip install -r requirements_enhanced.txt
# 
# Docker Installation:
# ===================
# 
# FROM python:3.11-slim
# 
# # Install system dependencies
# RUN apt-get update && apt-get install -y \
#     poppler-utils \
#     tesseract-ocr \
#     tesseract-ocr-pol \
#     libpoppler-cpp-dev \
#     libtesseract-dev \
#     libmagic1 \
#     && rm -rf /var/lib/apt/lists/*
# 
# # Install Python requirements
# COPY requirements_enhanced.txt .
# RUN pip install --no-cache-dir -r requirements_enhanced.txt
# 
# Performance Optimization:
# ========================
# 
# For production deployment:
# - Use Redis for caching processed documents
# - Implement Celery for background PDF processing
# - Configure PostgreSQL for persistent storage
# - Use nginx for static file serving
# - Enable Gradio queue for concurrent processing
# 
# Memory Requirements:
# ===================
# 
# Minimum: 4GB RAM
# Recommended: 8GB RAM (for large PDF processing)
# Storage: 10GB free space (for temporary files and cache)
# 
# Security Considerations:
# =======================
# 
# - Validate all uploaded files
# - Implement file size limits
# - Scan for malicious content
# - Use secure file storage
# - Encrypt sensitive financial data
# - Implement access controls
# 
# Monitoring and Logging:
# ======================
# 
# - Configure loguru for structured logging
# - Monitor PDF processing performance
# - Track OCR accuracy metrics
# - Alert on processing failures
# - Monitor system resource usage
