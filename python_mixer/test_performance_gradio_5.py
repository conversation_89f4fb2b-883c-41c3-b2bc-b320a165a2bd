#!/usr/bin/env python3
"""
Performance Test Suite for Gradio 5.0+ Enhanced Interface
Tests performance improvements and real-time capabilities.
"""

import sys
import time
import asyncio
import threading
import concurrent.futures
from pathlib import Path
from loguru import logger
import statistics

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class PerformanceTestSuite:
    """Performance test suite for Gradio 5.0+ interface."""
    
    def __init__(self):
        self.performance_results = {}
        self.load_test_results = {}
        
    def setup_logging(self):
        """Setup performance test logging."""
        logger.add(
            "logs/performance_test_gradio_5.log",
            rotation="1 day",
            retention="7 days",
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
        )
        
    def test_interface_startup_time(self):
        """Test interface startup performance."""
        logger.info("🚀 Testing interface startup time...")
        
        startup_times = []
        
        for i in range(5):
            start_time = time.time()
            
            try:
                from enhanced_human_comprehension_interface import HumanComprehensionInterface
                interface_manager = HumanComprehensionInterface()
                interface = interface_manager.create_interface()
                
                startup_time = time.time() - start_time
                startup_times.append(startup_time)
                
                logger.info(f"Startup {i+1}: {startup_time:.3f}s")
                
                # Clean up
                del interface
                del interface_manager
                
            except Exception as e:
                logger.error(f"Startup test {i+1} failed: {e}")
                return False
                
        avg_startup = statistics.mean(startup_times)
        min_startup = min(startup_times)
        max_startup = max(startup_times)
        
        self.performance_results['startup_avg'] = avg_startup
        self.performance_results['startup_min'] = min_startup
        self.performance_results['startup_max'] = max_startup
        
        logger.info(f"✅ Startup Performance:")
        logger.info(f"   Average: {avg_startup:.3f}s")
        logger.info(f"   Best: {min_startup:.3f}s")
        logger.info(f"   Worst: {max_startup:.3f}s")
        
        # Performance criteria: should start in under 1 second
        return avg_startup < 1.0
        
    def test_ai_chat_response_time(self):
        """Test AI chat response performance."""
        logger.info("🤖 Testing AI chat response times...")
        
        try:
            from enhanced_human_comprehension_interface import HumanComprehensionInterface
            interface_manager = HumanComprehensionInterface()
            
            test_messages = [
                "What is LG S12ET?",
                "Schedule maintenance",
                "Analyze customer email",
                "Equipment database search",
                "Generate quote for office"
            ]
            
            response_times = []
            
            for message in test_messages:
                start_time = time.time()
                response = interface_manager._chat_with_ai_assistant(message, [])
                response_time = time.time() - start_time
                
                response_times.append(response_time)
                logger.info(f"Message: '{message[:20]}...' -> {response_time:.3f}s")
                
            avg_response = statistics.mean(response_times)
            min_response = min(response_times)
            max_response = max(response_times)
            
            self.performance_results['ai_response_avg'] = avg_response
            self.performance_results['ai_response_min'] = min_response
            self.performance_results['ai_response_max'] = max_response
            
            logger.info(f"✅ AI Response Performance:")
            logger.info(f"   Average: {avg_response:.3f}s")
            logger.info(f"   Best: {min_response:.3f}s")
            logger.info(f"   Worst: {max_response:.3f}s")
            
            # Performance criteria: should respond in under 1 second
            return avg_response < 1.0
            
        except Exception as e:
            logger.error(f"AI chat performance test failed: {e}")
            return False
            
    def test_tool_use_performance(self):
        """Test tool use detection performance."""
        logger.info("🛠️ Testing tool use performance...")
        
        try:
            from enhanced_human_comprehension_interface import HumanComprehensionInterface
            interface_manager = HumanComprehensionInterface()
            
            tool_messages = [
                "analyze this email about equipment failure",
                "schedule appointment for tomorrow",
                "search equipment database for LG models",
                "generate quote for customer",
                "check calendar availability"
            ]
            
            tool_times = []
            
            for message in tool_messages:
                start_time = time.time()
                tool_response = interface_manager._determine_tool_use(message)
                tool_time = time.time() - start_time
                
                tool_times.append(tool_time)
                tool_name = tool_response['tool_name'] if tool_response else 'No tool'
                logger.info(f"Tool detection: '{message[:20]}...' -> {tool_name} ({tool_time:.3f}s)")
                
            avg_tool_time = statistics.mean(tool_times)
            
            self.performance_results['tool_detection_avg'] = avg_tool_time
            
            logger.info(f"✅ Tool Detection Performance: {avg_tool_time:.3f}s average")
            
            # Performance criteria: should detect tools in under 0.1 seconds
            return avg_tool_time < 0.1
            
        except Exception as e:
            logger.error(f"Tool use performance test failed: {e}")
            return False
            
    def test_concurrent_users(self):
        """Test concurrent user performance."""
        logger.info("👥 Testing concurrent user performance...")
        
        def simulate_user_session():
            """Simulate a user session."""
            try:
                from enhanced_human_comprehension_interface import HumanComprehensionInterface
                interface_manager = HumanComprehensionInterface()
                
                # Simulate user interactions
                start_time = time.time()
                
                # Chat interaction
                response1 = interface_manager._chat_with_ai_assistant("Test message", [])
                
                # Tool use
                tool_response = interface_manager._determine_tool_use("analyze email")
                
                # Calendar status
                calendar_status = interface_manager._get_calendar_status()
                
                session_time = time.time() - start_time
                return session_time
                
            except Exception as e:
                logger.error(f"User session simulation failed: {e}")
                return None
                
        # Test with 5 concurrent users
        concurrent_users = 5
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            start_time = time.time()
            
            futures = [executor.submit(simulate_user_session) for _ in range(concurrent_users)]
            session_times = []
            
            for future in concurrent.futures.as_completed(futures):
                session_time = future.result()
                if session_time is not None:
                    session_times.append(session_time)
                    
            total_time = time.time() - start_time
            
        if session_times:
            avg_session_time = statistics.mean(session_times)
            max_session_time = max(session_times)
            
            self.performance_results['concurrent_avg'] = avg_session_time
            self.performance_results['concurrent_max'] = max_session_time
            self.performance_results['concurrent_total'] = total_time
            
            logger.info(f"✅ Concurrent Users Performance:")
            logger.info(f"   Users: {len(session_times)}/{concurrent_users}")
            logger.info(f"   Average session: {avg_session_time:.3f}s")
            logger.info(f"   Slowest session: {max_session_time:.3f}s")
            logger.info(f"   Total time: {total_time:.3f}s")
            
            # Performance criteria: should handle concurrent users efficiently
            return avg_session_time < 2.0 and len(session_times) == concurrent_users
        else:
            logger.error("No successful concurrent sessions")
            return False
            
    def test_memory_efficiency(self):
        """Test memory usage efficiency."""
        logger.info("💾 Testing memory efficiency...")
        
        try:
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Create multiple interface instances
            interfaces = []
            for i in range(3):
                from enhanced_human_comprehension_interface import HumanComprehensionInterface
                interface_manager = HumanComprehensionInterface()
                interface = interface_manager.create_interface()
                interfaces.append((interface_manager, interface))
                
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Clean up
            for interface_manager, interface in interfaces:
                del interface
                del interface_manager
                
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            memory_increase = peak_memory - initial_memory
            memory_cleanup = peak_memory - final_memory
            
            self.performance_results['memory_initial'] = initial_memory
            self.performance_results['memory_peak'] = peak_memory
            self.performance_results['memory_final'] = final_memory
            self.performance_results['memory_increase'] = memory_increase
            
            logger.info(f"✅ Memory Efficiency:")
            logger.info(f"   Initial: {initial_memory:.1f} MB")
            logger.info(f"   Peak: {peak_memory:.1f} MB")
            logger.info(f"   Final: {final_memory:.1f} MB")
            logger.info(f"   Increase: {memory_increase:.1f} MB")
            
            # Performance criteria: should not use excessive memory
            return memory_increase < 200  # Less than 200MB increase
            
        except ImportError:
            logger.warning("psutil not available, skipping memory test")
            return True
        except Exception as e:
            logger.error(f"Memory efficiency test failed: {e}")
            return False
            
    def run_all_performance_tests(self):
        """Run all performance tests."""
        logger.info("🚀 Starting Gradio 5.0+ Performance Test Suite...")
        
        self.setup_logging()
        
        tests = [
            ('Interface Startup Time', self.test_interface_startup_time),
            ('AI Chat Response Time', self.test_ai_chat_response_time),
            ('Tool Use Performance', self.test_tool_use_performance),
            ('Concurrent Users', self.test_concurrent_users),
            ('Memory Efficiency', self.test_memory_efficiency),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name}: ERROR - {e}")
                
        # Generate performance report
        self.generate_performance_report(passed, total)
        
        return passed, total
        
    def generate_performance_report(self, passed, total):
        """Generate comprehensive performance report."""
        logger.info(f"\n{'='*60}")
        logger.info("⚡ GRADIO 5.0+ PERFORMANCE TEST REPORT")
        logger.info(f"{'='*60}")
        
        logger.info(f"📊 Overall Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
        
        if self.performance_results:
            logger.info("\n📈 Performance Metrics:")
            for metric_name, value in self.performance_results.items():
                if isinstance(value, float):
                    logger.info(f"  ⚡ {metric_name}: {value:.3f}s")
                else:
                    logger.info(f"  📊 {metric_name}: {value}")
                    
        # Performance analysis
        if 'startup_avg' in self.performance_results:
            startup_time = self.performance_results['startup_avg']
            if startup_time < 0.5:
                logger.info("🚀 EXCELLENT: Interface startup is very fast!")
            elif startup_time < 1.0:
                logger.info("✅ GOOD: Interface startup is acceptable")
            else:
                logger.warning("⚠️ SLOW: Interface startup could be improved")
                
        if 'ai_response_avg' in self.performance_results:
            response_time = self.performance_results['ai_response_avg']
            if response_time < 0.1:
                logger.info("🚀 EXCELLENT: AI responses are lightning fast!")
            elif response_time < 0.5:
                logger.info("✅ GOOD: AI responses are fast")
            else:
                logger.warning("⚠️ SLOW: AI responses could be faster")
                
        # Success criteria
        success_rate = passed / total
        if success_rate >= 0.8:
            logger.info("\n🎉 PERFORMANCE SUCCESS: Gradio 5.0+ is performing excellently!")
        elif success_rate >= 0.6:
            logger.info("\n⚠️ PERFORMANCE ISSUES: Some optimizations needed")
        else:
            logger.error("\n❌ PERFORMANCE PROBLEMS: Significant issues detected")
            
        logger.info(f"{'='*60}")

def main():
    """Main performance test execution."""
    test_suite = PerformanceTestSuite()
    passed, total = test_suite.run_all_performance_tests()
    
    # Exit with appropriate code
    if passed >= total * 0.8:  # 80% pass rate for performance tests
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
