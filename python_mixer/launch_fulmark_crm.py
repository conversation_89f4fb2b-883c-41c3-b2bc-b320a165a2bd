#!/usr/bin/env python3
"""
Launcher dla Modularnego Interfejsu Fulmark.pl HVAC CRM
======================================================

Prosty launcher do uruchamiania modularnego interfejsu CRM
z opcjami konfiguracji i diagnostyki.
"""

import sys
import os
import argparse
from pathlib import Path
from loguru import logger

# Dodaj ścieżkę do modułów
sys.path.insert(0, str(Path(__file__).parent))

try:
    from fulmark_crm_interface import FulmarkCRMInterface, main
except ImportError as e:
    logger.error(f"Nie można zaimportować interfejsu CRM: {e}")
    logger.info("Sprawdź czy wszystkie wymagane pakiety są zainstalowane:")
    logger.info("pip install gradio>=5.0.0 plotly pandas loguru")
    sys.exit(1)


def check_dependencies():
    """Sprawdź czy wszystkie wymagane pakiety są dostępne."""
    required_packages = {
        'gradio': '5.0.0',
        'plotly': '5.0.0',
        'pandas': '1.5.0',
        'loguru': '0.6.0'
    }
    
    missing_packages = []
    
    for package, min_version in required_packages.items():
        try:
            __import__(package)
            logger.success(f"✅ {package} - dostępny")
        except ImportError:
            missing_packages.append(package)
            logger.error(f"❌ {package} - brak pakietu")
    
    if missing_packages:
        logger.error("Brakujące pakiety:")
        for package in missing_packages:
            logger.error(f"  - {package}")
        logger.info("Zainstaluj brakujące pakiety:")
        logger.info(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def check_components():
    """Sprawdź dostępność komponentów modularnych."""
    components = {
        'base_styles': 'System stylów Material 3',
        'email_analysis': 'Analiza emaili z AI',
        'calendar_management': 'Zarządzanie kalendarzem'
    }
    
    available_components = []
    
    for component, description in components.items():
        try:
            module = __import__(f'gradio_components.{component}', fromlist=[component])
            available_components.append(component)
            logger.success(f"✅ {component} - {description}")
        except ImportError:
            logger.warning(f"⚠️ {component} - {description} (niedostępny)")
    
    logger.info(f"Dostępne komponenty: {len(available_components)}/{len(components)}")
    return available_components


def print_banner():
    """Wyświetl banner startowy."""
    banner = """
    🌟 Fulmark.pl HVAC CRM Interface 🌟
    ===================================
    
    Modularny interfejs CRM dla klimatyzacji
    Zoptymalizowany dla ludzkiego zrozumienia
    Material 3 Expressive Design System
    
    Firma: Fulmark.pl
    Lokalizacja: Warszawa
    Partnerzy: LG, Daikin
    """
    print(banner)


def main_launcher():
    """Główna funkcja launchera."""
    parser = argparse.ArgumentParser(
        description="Launcher dla Fulmark.pl HVAC CRM Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Przykłady użycia:
  python launch_fulmark_crm.py                    # Standardowe uruchomienie
  python launch_fulmark_crm.py --port 8080        # Niestandardowy port
  python launch_fulmark_crm.py --debug            # Tryb debugowania
  python launch_fulmark_crm.py --check-only       # Tylko sprawdzenie systemu
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=7860,
        help='Port serwera (domyślnie: 7860)'
    )
    
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='Adres hosta (domyślnie: 0.0.0.0)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Włącz tryb debugowania'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='Nie otwieraj automatycznie przeglądarki'
    )
    
    parser.add_argument(
        '--share',
        action='store_true',
        help='Udostępnij interfejs publicznie (Gradio share)'
    )
    
    parser.add_argument(
        '--check-only',
        action='store_true',
        help='Tylko sprawdź system i zakończ'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Tryb cichy (mniej logów)'
    )
    
    args = parser.parse_args()
    
    # Konfiguracja logowania
    if args.quiet:
        logger.remove()
        logger.add(sys.stderr, level="WARNING")
    elif args.debug:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG", format="<green>{time}</green> | <level>{level}</level> | {message}")
    
    # Banner
    if not args.quiet:
        print_banner()
    
    # Sprawdzenie systemu
    logger.info("🔍 Sprawdzanie systemu...")
    
    if not check_dependencies():
        logger.error("❌ Sprawdzenie zależności nie powiodło się")
        sys.exit(1)
    
    available_components = check_components()
    
    if len(available_components) < 2:
        logger.error("❌ Za mało dostępnych komponentów do uruchomienia")
        sys.exit(1)
    
    logger.success("✅ System gotowy do uruchomienia")
    
    if args.check_only:
        logger.info("🏁 Sprawdzenie zakończone (--check-only)")
        return
    
    # Uruchomienie interfejsu
    logger.info("🚀 Uruchamianie interfejsu Fulmark CRM...")
    
    try:
        crm_interface = FulmarkCRMInterface()
        
        launch_params = {
            'server_name': args.host,
            'server_port': args.port,
            'debug': args.debug,
            'inbrowser': not args.no_browser,
            'share': args.share,
            'quiet': args.quiet,
            'show_tips': not args.quiet,
            'enable_queue': True
        }
        
        logger.info(f"🌐 Interfejs będzie dostępny pod adresem: http://{args.host}:{args.port}")
        
        if args.share:
            logger.info("🌍 Interfejs będzie udostępniony publicznie (Gradio share)")
        
        crm_interface.launch(**launch_params)
        
    except KeyboardInterrupt:
        logger.info("⏹️ Interfejs zatrzymany przez użytkownika")
    except Exception as e:
        logger.error(f"❌ Błąd uruchamiania interfejsu: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main_launcher()
