#!/usr/bin/env python3
"""
Test Script for Invoice Processing System - Fulmark.pl CRM
=========================================================

Comprehensive testing script for the enhanced document processing
and invoice analysis capabilities.

Usage:
    python test_invoice_processing.py
    python test_invoice_processing.py --test-ocr
    python test_invoice_processing.py --test-suppliers
"""

import sys
import os
import argparse
from pathlib import Path
from loguru import logger

# Add path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from gradio_components.document_processor import DocumentProcessor
    from gradio_components.invoice_processing import InvoiceProcessingComponent
    from gradio_components.invoice_processing_helpers import InvoiceProcessingHelpers
except ImportError as e:
    logger.error(f"Import error: {e}")
    logger.info("Make sure all required packages are installed:")
    logger.info("pip install -r requirements_enhanced.txt")
    sys.exit(1)


def test_system_dependencies():
    """Test if all system dependencies are available."""
    logger.info("🔍 Testing system dependencies...")
    
    # Test PDF libraries
    try:
        import PyPDF2
        import pdfplumber
        logger.success("✅ PDF libraries available")
    except ImportError as e:
        logger.error(f"❌ PDF libraries missing: {e}")
        return False
    
    # Test OCR libraries
    try:
        import pytesseract
        from PIL import Image
        import pdf2image
        logger.success("✅ OCR libraries available")
    except ImportError as e:
        logger.error(f"❌ OCR libraries missing: {e}")
        return False
    
    # Test Tesseract installation
    try:
        version = pytesseract.get_tesseract_version()
        logger.success(f"✅ Tesseract version: {version}")
    except Exception as e:
        logger.error(f"❌ Tesseract not properly installed: {e}")
        return False
    
    # Test language support
    try:
        langs = pytesseract.get_languages()
        if 'pol' in langs:
            logger.success("✅ Polish language support available")
        else:
            logger.warning("⚠️ Polish language pack not installed")
    except Exception as e:
        logger.warning(f"⚠️ Could not check language support: {e}")
    
    return True


def test_document_processor():
    """Test the DocumentProcessor class."""
    logger.info("🔍 Testing DocumentProcessor...")
    
    try:
        processor = DocumentProcessor()
        logger.success("✅ DocumentProcessor initialized")
        
        # Test supplier database
        suppliers = processor.hvac_suppliers
        logger.info(f"📋 Loaded {len(suppliers)} HVAC suppliers")
        
        for supplier_id, supplier in suppliers.items():
            logger.info(f"  - {supplier_id}: {supplier['name']} ({supplier['category']})")
        
        # Test invoice patterns
        patterns = processor.invoice_patterns
        logger.info(f"🔍 Loaded {len(patterns)} invoice patterns")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ DocumentProcessor test failed: {e}")
        return False


def test_sample_text_analysis():
    """Test text analysis with sample invoice text."""
    logger.info("🔍 Testing text analysis...")
    
    sample_invoice_text = """
    FAKTURA VAT Nr FV/2024/001
    Data wystawienia: 15.01.2024
    
    Sprzedawca:
    LG Electronics Polska Sp. z o.o.
    ul. Jerozolimskie 181B
    02-222 Warszawa
    NIP: 123-456-78-90
    
    Nabywca:
    Fulmark Sp. z o.o.
    ul. Testowa 123
    00-001 Warszawa
    
    Lp. | Nazwa towaru/usługi | Ilość | Cena netto | Wartość netto
    1   | Klimatyzator LG S12ET Dual Cool | 2 | 2500,00 | 5000,00
    2   | Montaż i uruchomienie | 1 | 800,00 | 800,00
    
    Razem netto: 5800,00 PLN
    VAT 23%: 1334,00 PLN
    Razem brutto: 7134,00 PLN
    
    Termin płatności: 14 dni
    """
    
    try:
        processor = DocumentProcessor()
        
        # Simulate document processing
        result = {
            "success": True,
            "extracted_text": sample_invoice_text,
            "document_type": "unknown",
            "confidence_score": 0.0
        }
        
        # Analyze content
        analysis = processor._analyze_document_content(sample_invoice_text)
        result.update(analysis)
        
        logger.info(f"📄 Document type: {result['document_type']}")
        logger.info(f"🎯 Confidence: {result['confidence_score']*100:.1f}%")
        
        # Check invoice data
        invoice_data = result.get("invoice_data", {})
        if invoice_data:
            logger.info("💰 Extracted invoice data:")
            logger.info(f"  - Invoice number: {invoice_data.get('invoice_number')}")
            logger.info(f"  - Date: {invoice_data.get('date')}")
            logger.info(f"  - Total amount: {invoice_data.get('total_amount')} {invoice_data.get('currency')}")
            logger.info(f"  - Vendor: {invoice_data.get('vendor_name')}")
            logger.info(f"  - Line items: {len(invoice_data.get('line_items', []))}")
        
        # Check supplier info
        supplier_info = result.get("supplier_info", {})
        if supplier_info.get("identified"):
            logger.info("🏭 Supplier identified:")
            logger.info(f"  - Name: {supplier_info.get('supplier_name')}")
            logger.info(f"  - Category: {supplier_info.get('category')}")
            logger.info(f"  - Confidence: {supplier_info.get('confidence')*100:.1f}%")
            logger.info(f"  - Method: {supplier_info.get('match_method')}")
        else:
            logger.warning("⚠️ Supplier not identified")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Text analysis test failed: {e}")
        return False


def test_invoice_helpers():
    """Test invoice processing helper methods."""
    logger.info("🔍 Testing invoice processing helpers...")
    
    try:
        # Sample result for testing
        sample_result = {
            "document_type": "invoice",
            "confidence_score": 0.9,
            "extracted_text": "lg klimatyzator serwis naprawa",
            "invoice_data": {
                "total_amount": 7134.00,
                "currency": "PLN",
                "invoice_number": "FV/2024/001",
                "date": "2024-01-15",
                "vendor_name": "LG Electronics",
                "line_items": [
                    {"description": "Klimatyzator LG S12ET", "quantity": 2, "unit_price": 2500.00},
                    {"description": "Montaż", "quantity": 1, "unit_price": 800.00}
                ]
            },
            "supplier_info": {
                "identified": True,
                "supplier_name": "LG Electronics",
                "category": "equipment_manufacturer",
                "confidence": 0.95
            }
        }
        
        # Test auto categorization
        category = InvoiceProcessingHelpers.auto_categorize_invoice(sample_result)
        logger.info(f"🏷️ Auto category: {category['category']} ({category['confidence']*100:.1f}%)")
        logger.info(f"📝 Reasoning: {', '.join(category['reasoning'])}")
        
        # Test financial summary
        financial = InvoiceProcessingHelpers.calculate_financial_summary(sample_result)
        logger.info(f"💰 Financial summary:")
        logger.info(f"  - Total: {financial['total_amount']} {financial['currency']}")
        logger.info(f"  - Net: {financial['net_amount']:.2f}")
        logger.info(f"  - Tax: {financial['tax_amount']:.2f}")
        logger.info(f"  - Items: {financial['line_items_count']}")
        
        # Test supplier validation
        validation = InvoiceProcessingHelpers.validate_supplier_details(sample_result["supplier_info"])
        logger.info(f"✅ Supplier validation: {validation['is_valid']}")
        if validation['warnings']:
            logger.warning(f"⚠️ Warnings: {', '.join(validation['warnings'])}")
        if validation['recommendations']:
            logger.info(f"💡 Recommendations: {', '.join(validation['recommendations'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Invoice helpers test failed: {e}")
        return False


def test_ocr_functionality():
    """Test OCR functionality with a sample image."""
    logger.info("🔍 Testing OCR functionality...")
    
    try:
        import pytesseract
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple test image with Polish text
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        # Add sample invoice text
        text_lines = [
            "FAKTURA VAT Nr FV/2024/001",
            "Data: 15.01.2024",
            "Sprzedawca: LG Electronics Polska",
            "NIP: 123-456-78-90",
            "Razem: 7134,00 PLN"
        ]
        
        y_position = 50
        for line in text_lines:
            draw.text((50, y_position), line, fill='black', font=font)
            y_position += 40
        
        # Test OCR
        ocr_config = '--oem 3 --psm 6 -l pol+eng'
        extracted_text = pytesseract.image_to_string(img, config=ocr_config)
        
        logger.info("📝 OCR extracted text:")
        logger.info(extracted_text)
        
        # Check if key elements were recognized
        if "FAKTURA" in extracted_text.upper():
            logger.success("✅ Invoice header recognized")
        if "123-456-78-90" in extracted_text:
            logger.success("✅ Tax ID recognized")
        if "7134" in extracted_text:
            logger.success("✅ Amount recognized")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ OCR test failed: {e}")
        return False


def run_comprehensive_test():
    """Run comprehensive test suite."""
    logger.info("🚀 Starting comprehensive test suite for Invoice Processing System")
    
    tests = [
        ("System Dependencies", test_system_dependencies),
        ("Document Processor", test_document_processor),
        ("Text Analysis", test_sample_text_analysis),
        ("Invoice Helpers", test_invoice_helpers),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            if result:
                logger.success(f"✅ {test_name} - PASSED")
            else:
                logger.error(f"❌ {test_name} - FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.success("🎉 All tests passed! Invoice processing system is ready.")
        return True
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
        return False


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Test Invoice Processing System")
    parser.add_argument("--test-ocr", action="store_true", help="Test OCR functionality")
    parser.add_argument("--test-suppliers", action="store_true", help="Test supplier database")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    if args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG")
    
    if args.test_ocr:
        logger.info("🔍 Testing OCR functionality only...")
        success = test_ocr_functionality()
    elif args.test_suppliers:
        logger.info("🔍 Testing supplier database only...")
        success = test_document_processor()
    else:
        success = run_comprehensive_test()
        
        # Optional OCR test
        if success:
            logger.info("\n" + "="*50)
            logger.info("Optional: Testing OCR functionality")
            logger.info("="*50)
            test_ocr_functionality()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
