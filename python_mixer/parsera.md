# Najlepsze biblioteki Python do parsowania dokumentów z załączników

## Wprowadzenie

Niniejszy raport przedstawia analizę najlepszych bibliotek Python do parsowania dokumentów z załączników, takich jak faktury w formatach PDF, DOC/DOCX i Excel. Skupiamy się na bibliotekach, które oferują wysoką dokładność ekstrakcji danych, obsługę tabel i struktur typowych dla dokumentów biznesowych oraz łatwość integracji z systemami automatyzacji.

## 1. Biblioteki do parsowania PDF

### 1.1. PyMuPDF (fitz)

**GitHub**: [pymupdf/PyMuPDF](https://github.com/pymupdf/PyMuPDF)

PyMuPDF to kompleksowa biblioteka do pracy z dokumentami PDF, oparta na silniku MuPDF.

**Kluczowe funkcje**:
- Bardzo szybka ekstrakcja tekstu z zachowaniem układu
- Obsługa tabel i struktur dokumentu
- Dostęp do metadanych, załączników i formularzy
- Możliwość edycji i tworzenia dokumentów PDF
- Obsługa obrazów i grafiki wektorowej

**Zalety dla faktur**:
- Doskonałe zachowanie układu tekstu, co jest kluczowe dla faktur
- Wysoka wydajność nawet dla dużych dokumentów
- Dokładna ekstrakcja danych tabelarycznych

**Przykładowy kod**:
```python
import fitz  # PyMuPDF

def extract_text_from_pdf(pdf_path):
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    return text

def extract_tables_from_pdf(pdf_path):
    doc = fitz.open(pdf_path)
    tables = []
    for page in doc:
        # Wykrywanie tabel na podstawie linii i układu tekstu
        tables.extend(page.find_tables())
    return tables
```

### 1.2. PDFPlumber

**GitHub**: [jsvine/pdfplumber](https://github.com/jsvine/pdfplumber)

PDFPlumber to biblioteka specjalizująca się w ekstrakcji danych tabelarycznych z dokumentów PDF.

**Kluczowe funkcje**:
- Zaawansowane wykrywanie i ekstrakcja tabel
- Dokładna analiza układu strony
- Ekstrakcja tekstu z zachowaniem pozycji
- Obsługa linii, kształtów i obrazów

**Zalety dla faktur**:
- Precyzyjna ekstrakcja tabel z faktur
- Możliwość identyfikacji pól na podstawie ich położenia
- Dobra obsługa dokumentów o złożonym układzie

**Przykładowy kod**:
```python
import pdfplumber

def extract_invoice_data(pdf_path):
    with pdfplumber.open(pdf_path) as pdf:
        first_page = pdf.pages[0]
        
        # Ekstrakcja tekstu
        text = first_page.extract_text()
        
        # Ekstrakcja tabel
        tables = first_page.extract_tables()
        
        # Ekstrakcja konkretnych obszarów (np. pole z kwotą)
        invoice_amount = first_page.crop((x0, y0, x1, y1)).extract_text()
        
        return {
            "text": text,
            "tables": tables,
            "amount": invoice_amount
        }
```

### 1.3. Camelot

**GitHub**: [camelot-dev/camelot](https://github.com/camelot-dev/camelot)

Camelot to biblioteka specjalizująca się w ekstrakcji tabel z dokumentów PDF.

**Kluczowe funkcje**:
- Dwa silniki ekstrakcji: oparty na liniach i oparty na siatce
- Wysoka dokładność ekstrakcji tabel
- Możliwość dostosowania parametrów ekstrakcji
- Eksport do różnych formatów (CSV, Excel, HTML)

**Zalety dla faktur**:
- Najlepsza biblioteka do ekstrakcji tabel z faktur
- Możliwość obsługi skomplikowanych układów tabel
- Dokładna ekstrakcja danych liczbowych

**Przykładowy kod**:
```python
import camelot

def extract_invoice_tables(pdf_path):
    # Użycie silnika opartego na liniach
    tables_line = camelot.read_pdf(pdf_path, flavor='lattice')
    
    # Użycie silnika opartego na siatce
    tables_stream = camelot.read_pdf(pdf_path, flavor='stream')
    
    # Eksport do CSV
    tables_line[0].to_csv('invoice_table.csv')
    
    return {
        "lattice_tables": tables_line,
        "stream_tables": tables_stream
    }
```

### 1.4. invoice2data

**GitHub**: [invoice-x/invoice2data](https://github.com/invoice-x/invoice2data)

invoice2data to wyspecjalizowana biblioteka do ekstrakcji danych z faktur PDF.

**Kluczowe funkcje**:
- System szablonów YAML/JSON do dopasowywania faktur
- Obsługa różnych metod ekstrakcji tekstu (pdftotext, pdfminer, pdfplumber)
- Integracja z OCR (tesseract, Google Cloud Vision)
- Eksport do CSV, JSON, XML

**Zalety dla faktur**:
- Zaprojektowana specjalnie do przetwarzania faktur
- Możliwość definiowania szablonów dla różnych dostawców
- Ekstrakcja kluczowych pól (data, numer faktury, kwota)
- Obsługa pozycji faktur i tabel

**Przykładowy kod**:
```python
from invoice2data import extract_data
from invoice2data.extract.loader import read_templates

# Użycie domyślnych szablonów
result = extract_data('faktura.pdf')

# Użycie własnych szablonów
templates = read_templates('/sciezka/do/szablonow/')
result = extract_data('faktura.pdf', templates=templates)

print(f"Numer faktury: {result['invoice_number']}")
print(f"Data: {result['date']}")
print(f"Kwota: {result['amount']}")
```

### 1.5. PyPDF2

**GitHub**: [py-pdf/PyPDF2](https://github.com/py-pdf/PyPDF2)

PyPDF2 to podstawowa biblioteka do pracy z dokumentami PDF w Pythonie.

**Kluczowe funkcje**:
- Ekstrakcja tekstu z dokumentów PDF
- Łączenie i dzielenie dokumentów
- Obracanie i przycinanie stron
- Szyfrowanie i deszyfrowanie

**Zalety dla faktur**:
- Lekka i prosta w użyciu
- Dobra do podstawowej ekstrakcji tekstu
- Niskie zużycie zasobów

**Wady dla faktur**:
- Ograniczona obsługa układu tekstu
- Brak natywnego wsparcia dla tabel
- Problemy z dokumentami zawierającymi skomplikowane formatowanie

**Przykładowy kod**:
```python
from PyPDF2 import PdfReader

def extract_text_from_pdf(pdf_path):
    reader = PdfReader(pdf_path)
    text = ""
    for page in reader.pages:
        text += page.extract_text()
    return text
```

## 2. Biblioteki do parsowania DOC/DOCX

### 2.1. python-docx

**GitHub**: [python-openxml/python-docx](https://github.com/python-openxml/python-docx)

python-docx to standardowa biblioteka do pracy z dokumentami Word w formacie DOCX.

**Kluczowe funkcje**:
- Odczyt i tworzenie dokumentów DOCX
- Dostęp do tekstu, tabel, obrazów
- Manipulacja stylami i formatowaniem
- Obsługa nagłówków i stopek

**Zalety dla faktur**:
- Dobra obsługa tabel, które często występują w fakturach
- Możliwość ekstrakcji tekstu z zachowaniem struktury
- Dostęp do metadanych dokumentu

**Przykładowy kod**:
```python
from docx import Document

def extract_tables_from_docx(docx_path):
    doc = Document(docx_path)
    tables_data = []
    
    for table in doc.tables:
        table_data = []
        for row in table.rows:
            row_data = [cell.text for cell in row.cells]
            table_data.append(row_data)
        tables_data.append(table_data)
    
    return tables_data

def extract_text_from_docx(docx_path):
    doc = Document(docx_path)
    full_text = []
    
    for para in doc.paragraphs:
        full_text.append(para.text)
    
    return '\n'.join(full_text)
```

### 2.2. docx2txt

**PyPI**: [docx2txt](https://pypi.org/project/docx2txt/)

docx2txt to prosta biblioteka do konwersji dokumentów DOCX na tekst.

**Kluczowe funkcje**:
- Ekstrakcja czystego tekstu z dokumentów DOCX
- Obsługa obrazów (opcjonalnie)
- Lekka i minimalistyczna

**Zalety dla faktur**:
- Prosta w użyciu
- Szybka ekstrakcja tekstu
- Niskie zużycie zasobów

**Wady dla faktur**:
- Ograniczona obsługa tabel
- Utrata formatowania i układu
- Brak obsługi starszego formatu DOC

**Przykładowy kod**:
```python
import docx2txt

def extract_text_from_docx(docx_path):
    text = docx2txt.process(docx_path)
    return text
```

### 2.3. textract

**GitHub**: [deanmalmgren/textract](https://github.com/deanmalmgren/textract)

textract to uniwersalna biblioteka do ekstrakcji tekstu z różnych formatów dokumentów, w tym DOC i DOCX.

**Kluczowe funkcje**:
- Obsługa wielu formatów (DOC, DOCX, PDF, ODT, TXT i inne)
- Jednolite API dla różnych typów dokumentów
- Integracja z narzędziami zewnętrznymi

**Zalety dla faktur**:
- Możliwość przetwarzania zarówno DOC, jak i DOCX
- Uniwersalne rozwiązanie dla różnych formatów
- Prosta integracja

**Wady dla faktur**:
- Ograniczona obsługa tabel i struktury dokumentu
- Wymaga instalacji dodatkowych zależności
- Mniejsza dokładność niż wyspecjalizowane biblioteki

**Przykładowy kod**:
```python
import textract

def extract_text_from_document(file_path):
    text = textract.process(file_path).decode('utf-8')
    return text
```

### 2.4. Docling

**GitHub**: [nosahama/docling](https://github.com/nosahama/docling)

Docling to nowsza biblioteka do parsowania różnych formatów dokumentów, w tym DOCX.

**Kluczowe funkcje**:
- Obsługa wielu formatów (DOCX, PDF, HTML, Markdown, PPTX)
- Zachowanie struktury dokumentu
- Ekstrakcja metadanych
- Konwersja między formatami

**Zalety dla faktur**:
- Lepsza obsługa struktury dokumentu
- Możliwość ekstrakcji tabel i list
- Nowoczesne podejście do parsowania dokumentów

**Przykładowy kod**:
```python
from docling import Document

def parse_docx_with_structure(docx_path):
    doc = Document(docx_path)
    
    # Ekstrakcja tekstu z zachowaniem struktury
    content = doc.parse()
    
    # Ekstrakcja metadanych
    metadata = doc.metadata
    
    return {
        "content": content,
        "metadata": metadata
    }
```

## 3. Biblioteki do parsowania Excel

### 3.1. pandas

**GitHub**: [pandas-dev/pandas](https://github.com/pandas-dev/pandas)

pandas to potężna biblioteka do analizy danych, która doskonale radzi sobie z plikami Excel.

**Kluczowe funkcje**:
- Odczyt i zapis plików Excel (XLS, XLSX)
- Zaawansowane przetwarzanie danych
- Obsługa wielu arkuszy
- Filtrowanie, grupowanie i agregacja danych

**Zalety dla faktur**:
- Doskonała obsługa danych tabelarycznych
- Zaawansowane funkcje analizy danych
- Łatwa konwersja do innych formatów (CSV, JSON)

**Przykładowy kod**:
```python
import pandas as pd

def extract_invoice_data_from_excel(excel_path):
    # Odczyt wszystkich arkuszy
    all_sheets = pd.read_excel(excel_path, sheet_name=None)
    
    # Odczyt konkretnego arkusza
    invoice_data = pd.read_excel(excel_path, sheet_name='Faktura')
    
    # Filtrowanie danych
    items = invoice_data[invoice_data['Typ'] == 'Produkt']
    
    # Obliczanie sum
    total = invoice_data['Kwota'].sum()
    
    return {
        "all_data": all_sheets,
        "invoice_items": items,
        "total_amount": total
    }
```

### 3.2. openpyxl

**GitHub**: [openpyxl/openpyxl](https://github.com/openpyxl/openpyxl)

openpyxl to biblioteka specjalizująca się w pracy z plikami Excel w formacie XLSX.

**Kluczowe funkcje**:
- Odczyt i zapis plików XLSX
- Dostęp do komórek, wierszy i kolumn
- Obsługa formatowania i stylów
- Praca z wykresami i obrazami

**Zalety dla faktur**:
- Dokładna kontrola nad strukturą arkusza
- Możliwość odczytu sformatowanych danych
- Dostęp do formuł i ich wyników

**Przykładowy kod**:
```python
from openpyxl import load_workbook

def extract_structured_data_from_excel(excel_path):
    wb = load_workbook(excel_path, data_only=True)
    sheet = wb.active
    
    # Ekstrakcja danych nagłówkowych (np. z konkretnych komórek)
    invoice_number = sheet['B2'].value
    date = sheet['B3'].value
    customer = sheet['B4'].value
    
    # Ekstrakcja tabeli pozycji faktury
    items = []
    for row in sheet.iter_rows(min_row=8, max_row=sheet.max_row, min_col=1, max_col=5):
        if row[0].value:  # Pomijanie pustych wierszy
            items.append({
                "name": row[0].value,
                "quantity": row[1].value,
                "price": row[2].value,
                "tax": row[3].value,
                "total": row[4].value
            })
    
    # Ekstrakcja podsumowania
    total_net = sheet[f'E{sheet.max_row-2}'].value
    total_tax = sheet[f'E{sheet.max_row-1}'].value
    total_gross = sheet[f'E{sheet.max_row}'].value
    
    return {
        "invoice_number": invoice_number,
        "date": date,
        "customer": customer,
        "items": items,
        "totals": {
            "net": total_net,
            "tax": total_tax,
            "gross": total_gross
        }
    }
```

### 3.3. xlrd

**PyPI**: [xlrd](https://pypi.org/project/xlrd/)

xlrd to biblioteka do odczytu starszych plików Excel w formacie XLS.

**Kluczowe funkcje**:
- Odczyt plików XLS (Excel 97-2003)
- Obsługa wielu arkuszy
- Dostęp do sformatowanych wartości
- Obsługa typów danych (daty, liczby, tekst)

**Zalety dla faktur**:
- Obsługa starszego formatu XLS
- Stabilna i sprawdzona biblioteka
- Dobra wydajność

**Wady dla faktur**:
- Brak wsparcia dla nowszego formatu XLSX
- Brak możliwości zapisu
- Ograniczona obsługa formatowania

**Przykładowy kod**:
```python
import xlrd

def extract_data_from_xls(xls_path):
    workbook = xlrd.open_workbook(xls_path)
    sheet = workbook.sheet_by_index(0)
    
    # Ekstrakcja danych z konkretnych komórek
    invoice_number = sheet.cell_value(1, 1)  # B2
    date = sheet.cell_value(2, 1)  # B3
    
    # Konwersja daty Excel na format Pythona
    if isinstance(date, float):
        date = xlrd.xldate_as_datetime(date, workbook.datemode)
    
    # Ekstrakcja tabeli danych
    data = []
    for row_idx in range(7, sheet.nrows):
        if sheet.cell_value(row_idx, 0):  # Pomijanie pustych wierszy
            row_data = [sheet.cell_value(row_idx, col_idx) for col_idx in range(sheet.ncols)]
            data.append(row_data)
    
    return {
        "invoice_number": invoice_number,
        "date": date,
        "data": data
    }
```

### 3.4. IronXL

**Strona**: [IronXL](https://ironsoftware.com/python/excel/)

IronXL to komercyjna biblioteka do pracy z plikami Excel, oferująca zaawansowane funkcje.

**Kluczowe funkcje**:
- Obsługa XLS, XLSX, CSV, TSV
- Zaawansowane formatowanie
- Obsługa formuł
- Konwersja do PDF, CSV, JSON

**Zalety dla faktur**:
- Wysoka dokładność odczytu
- Obsługa skomplikowanych formatowań
- Dobra dokumentacja i wsparcie

**Wady dla faktur**:
- Rozwiązanie płatne
- Większy rozmiar i zależności

**Przykładowy kod**:
```python
from ironxl import WorkBook

def extract_invoice_data_with_ironxl(excel_path):
    wb = WorkBook.Load(excel_path)
    sheet = wb.WorkSheets[0]
    
    # Odczyt danych z konkretnych komórek
    invoice_number = sheet.GetCellValue("B2")
    date = sheet.GetCellValue("B3")
    
    # Odczyt zakresu danych
    data_range = sheet["A8:E20"]
    
    # Konwersja do listy słowników
    headers = ["name", "quantity", "price", "tax", "total"]
    items = []
    
    for row in data_range.Rows:
        if row[0].Value:  # Pomijanie pustych wierszy
            item = {headers[i]: cell.Value for i, cell in enumerate(row.Cells)}
            items.append(item)
    
    return {
        "invoice_number": invoice_number,
        "date": date,
        "items": items
    }
```

## 4. Porównanie bibliotek dla każdego formatu

### 4.1. Biblioteki PDF

| Biblioteka | Ekstrakcja tekstu | Ekstrakcja tabel | OCR | Szablony | Wydajność | Dokładność | Łatwość użycia |
|------------|-------------------|------------------|-----|----------|-----------|------------|----------------|
| PyMuPDF    | ★★★★★             | ★★★★☆            | ☆☆☆☆☆ | ☆☆☆☆☆     | ★★★★★     | ★★★★☆      | ★★★★☆          |
| PDFPlumber | ★★★★☆             | ★★★★★            | ☆☆☆☆☆ | ☆☆☆☆☆     | ★★★☆☆     | ★★★★★      | ★★★★★          |
| Camelot    | ★★★☆☆             | ★★★★★            | ☆☆☆☆☆ | ☆☆☆☆☆     | ★★★☆☆     | ★★★★★      | ★★★★☆          |
| invoice2data| ★★★☆☆            | ★★★☆☆            | ★★★★☆ | ★★★★★     | ★★★☆☆     | ★★★★☆      | ★★★★★          |
| PyPDF2     | ★★★☆☆             | ★☆☆☆☆            | ☆☆☆☆☆ | ☆☆☆☆☆     | ★★★★☆     | ★★☆☆☆      | ★★★★★          |

**Rekomendacja dla faktur PDF**:
- Dla ogólnej ekstrakcji tekstu z zachowaniem układu: **PyMuPDF**
- Dla ekstrakcji tabel z faktur: **PDFPlumber** lub **Camelot**
- Dla automatyzacji przetwarzania faktur z szablonami: **invoice2data**

### 4.2. Biblioteki DOC/DOCX

| Biblioteka | Format DOC | Format DOCX | Ekstrakcja tabel | Zachowanie struktury | Wydajność | Dokładność | Łatwość użycia |
|------------|------------|-------------|------------------|----------------------|-----------|------------|----------------|
| python-docx| ☆☆☆☆☆      | ★★★★★       | ★★★★☆            | ★★★★☆                | ★★★★☆     | ★★★★☆      | ★★★★☆          |
| docx2txt   | ☆☆☆☆☆      | ★★★★☆       | ★☆☆☆☆            | ★★☆☆☆                | ★★★★★     | ★★☆☆☆      | ★★★★★          |
| textract   | ★★★★☆      | ★★★★☆       | ★★☆☆☆            | ★★☆☆☆                | ★★★☆☆     | ★★★☆☆      | ★★★★★          |
| Docling    | ☆☆☆☆☆      | ★★★★☆       | ★★★☆☆            | ★★★★☆                | ★★★☆☆     | ★★★★☆      | ★★★☆☆          |

**Rekomendacja dla faktur DOC/DOCX**:
- Dla dokumentów DOCX z tabelami: **python-docx**
- Dla prostej ekstrakcji tekstu z DOCX: **docx2txt**
- Dla obsługi zarówno DOC, jak i DOCX: **textract**

### 4.3. Biblioteki Excel

| Biblioteka | Format XLS | Format XLSX | Analiza danych | Obsługa formuł | Wydajność | Dokładność | Łatwość użycia |
|------------|------------|-------------|----------------|----------------|-----------|------------|----------------|
| pandas     | ★★★★☆      | ★★★★★       | ★★★★★          | ★★★☆☆          | ★★★★☆     | ★★★★★      | ★★★★★          |
| openpyxl   | ☆☆☆☆☆      | ★★★★★       | ★★★☆☆          | ★★★★☆          | ★★★★☆     | ★★★★★      | ★★★★☆          |
| xlrd       | ★★★★★      | ★☆☆☆☆       | ★★☆☆☆          | ★★★☆☆          | ★★★★★     | ★★★★☆      | ★★★☆☆          |
| IronXL     | ★★★★★      | ★★★★★       | ★★★☆☆          | ★★★★★          | ★★★☆☆     | ★★★★★      | ★★★★☆          |

**Rekomendacja dla faktur Excel**:
- Dla analizy danych i przetwarzania: **pandas**
- Dla dokładnej kontroli nad strukturą XLSX: **openpyxl**
- Dla starszych plików XLS: **xlrd**

## 5. Integracja z systemami multi-agentowymi

Biblioteki do parsowania dokumentów można łatwo zintegrować z systemami multi-agentowymi, takimi jak LangGraph czy CrewAI, aby stworzyć kompleksowe rozwiązanie do automatycznego przetwarzania faktur i innych dokumentów biznesowych.

### 5.1. Przykład integracji z CrewAI

```python
from crewai import Agent, Task, Crew, Process
import pandas as pd
import fitz  # PyMuPDF
from invoice2data import extract_data

# Definicja agentów
document_processor_agent = Agent(
    role="Document Processor",
    goal="Extract structured data from invoice documents",
    backstory="You are an expert in document processing with deep knowledge of invoice formats.",
    verbose=True
)

data_analyst_agent = Agent(
    role="Data Analyst",
    goal="Analyze and validate extracted invoice data",
    backstory="You are a financial data analyst specializing in invoice validation and error detection.",
    verbose=True
)

# Funkcje pomocnicze
def process_pdf_invoice(file_path):
    # Próba użycia invoice2data
    try:
        data = extract_data(file_path)
        if data:
            return data
    except Exception as e:
        print(f"invoice2data failed: {e}")
    
    # Fallback do PyMuPDF
    try:
        doc = fitz.open(file_path)
        text = ""
        for page in doc:
            text += page.get_text()
        return {"raw_text": text}
    except Exception as e:
        print(f"PyMuPDF failed: {e}")
        return {"error": str(e)}

def process_excel_invoice(file_path):
    try:
        df = pd.read_excel(file_path)
        return df.to_dict(orient="records")
    except Exception as e:
        print(f"pandas failed: {e}")
        return {"error": str(e)}

# Definicja zadań
extraction_task = Task(
    description="Extract structured data from the provided invoice document",
    agent=document_processor_agent,
    expected_output="JSON structure with invoice details"
)

validation_task = Task(
    description="Validate the extracted invoice data for accuracy and completeness",
    agent=data_analyst_agent,
    expected_output="Validation report and corrected data",
    context=[extraction_task]
)

# Utworzenie i uruchomienie crew
invoice_crew = Crew(
    agents=[document_processor_agent, data_analyst_agent],
    tasks=[extraction_task, validation_task],
    process=Process.sequential
)

# Funkcja integrująca
def process_invoice_with_agents(file_path):
    file_extension = file_path.split('.')[-1].lower()
    
    if file_extension in ['pdf']:
        raw_data = process_pdf_invoice(file_path)
    elif file_extension in ['xlsx', 'xls']:
        raw_data = process_excel_invoice(file_path)
    else:
        return {"error": "Unsupported file format"}
    
    # Przekazanie danych do agentów
    extraction_task.context = [{"raw_data": raw_data, "file_path": file_path}]
    
    # Uruchomienie crew
    result = invoice_crew.kickoff()
    
    return result
```

### 5.2. Przykład integracji z LangGraph

```python
import langroid as lr
from langroid import ChatAgent, Task
import fitz  # PyMuPDF
from docx import Document
import pandas as pd

async def process_document_with_agents(file_path):
    # Konfiguracja agentów
    llm_cfg = lr.ChatAgentConfig(llm=lr.OpenAIGPTConfig(chat_model="gpt-4"))
    
    # Agent do ekstrakcji danych
    extractor_agent = lr.ChatAgent(llm_cfg)
    extractor_task = lr.Task(
        extractor_agent, 
        name="Extractor", 
        system_message="Extract structured data from invoice documents."
    )
    
    # Agent do walidacji danych
    validator_agent = lr.ChatAgent(llm_cfg)
    validator_task = lr.Task(
        validator_agent, 
        name="Validator", 
        system_message="Validate and correct extracted invoice data."
    )
    
    # Ekstrakcja danych z dokumentu
    file_extension = file_path.split('.')[-1].lower()
    document_content = ""
    
    if file_extension == 'pdf':
        doc = fitz.open(file_path)
        for page in doc:
            document_content += page.get_text()
    
    elif file_extension == 'docx':
        doc = Document(file_path)
        for para in doc.paragraphs:
            document_content += para.text + "\n"
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    document_content += cell.text + " | "
                document_content += "\n"
    
    elif file_extension in ['xlsx', 'xls']:
        df = pd.read_excel(file_path)
        document_content = df.to_string()
    
    else:
        return {"error": "Unsupported file format"}
    
    # Ekstrakcja danych przez pierwszego agenta
    extraction_result = await extractor_task.llm_response(
        f"Extract structured invoice data from the following document content: {document_content[:4000]}..."
    )
    
    # Walidacja danych przez drugiego agenta
    validation_result = await validator_task.llm_response(
        f"Validate and correct the following extracted invoice data: {extraction_result}"
    )
    
    return {
        "extraction": extraction_result,
        "validation": validation_result
    }
```

## 6. Rekomendacje końcowe

Na podstawie przeprowadzonej analizy, rekomendujemy następujące biblioteki do parsowania dokumentów z załączników, takich jak faktury:

### 6.1. Dla dokumentów PDF

**Najlepsza ogólna biblioteka**: **PyMuPDF (fitz)**
- Oferuje najlepszą kombinację szybkości, dokładności i funkcjonalności
- Doskonale zachowuje układ tekstu, co jest kluczowe dla faktur
- Obsługuje tabele i struktury dokumentu

**Najlepsza biblioteka do ekstrakcji tabel**: **Camelot**
- Specjalizuje się w ekstrakcji tabel z dokumentów PDF
- Oferuje dwa silniki ekstrakcji dla różnych typów tabel
- Wysoka dokładność dla danych tabelarycznych

**Najlepsza biblioteka specjalizowana dla faktur**: **invoice2data**
- Zaprojektowana specjalnie do przetwarzania faktur
- System szablonów do dopasowywania różnych formatów faktur
- Integracja z OCR dla skanowanych dokumentów

### 6.2. Dla dokumentów DOC/DOCX

**Najlepsza biblioteka dla DOCX**: **python-docx**
- Pełna obsługa dokumentów DOCX
- Dobra ekstrakcja tabel i struktury dokumentu
- Aktywnie rozwijana i wspierana

**Najlepsza uniwersalna biblioteka**: **textract**
- Obsługa zarówno DOC, jak i DOCX
- Jednolite API dla różnych formatów
- Dobra integracja z innymi narzędziami

### 6.3. Dla dokumentów Excel

**Najlepsza biblioteka do analizy danych**: **pandas**
- Potężne funkcje analizy danych
- Doskonała obsługa plików Excel (XLS, XLSX)
- Łatwa integracja z innymi bibliotekami

**Najlepsza biblioteka do dokładnej kontroli**: **openpyxl**
- Precyzyjna kontrola nad strukturą arkusza
- Dostęp do formatowania i stylów
- Obsługa formuł i ich wyników

### 6.4. Strategia wielowarstwowa

Dla najlepszych wyników w przetwarzaniu faktur i innych dokumentów biznesowych, rekomendujemy podejście wielowarstwowe:

1. **Warstwa ekstrakcji**: Użyj wyspecjalizowanych bibliotek dla każdego formatu:
   - PDF: PyMuPDF + Camelot
   - DOCX: python-docx
   - Excel: pandas + openpyxl

2. **Warstwa strukturyzacji**: Zastosuj invoice2data dla faktur PDF lub własne szablony dla innych formatów

3. **Warstwa walidacji**: Wykorzystaj systemy multi-agentowe (CrewAI, LangGraph) do walidacji i korekty danych

4. **Warstwa integracji**: Połącz wyniki z bazami danych lub systemami księgowymi

Takie podejście zapewnia wysoką dokładność ekstrakcji danych z różnych formatów dokumentów, elastyczność w obsłudze różnych układów faktur oraz niezawodność dzięki walidacji przez systemy AI.

## 7. Przykładowy kod kompleksowego rozwiązania

Poniżej przedstawiamy przykładowy kod kompleksowego rozwiązania do parsowania faktur w różnych formatach:

```python
import os
import json
import fitz  # PyMuPDF
import camelot
from invoice2data import extract_data
from docx import Document
import pandas as pd
import openpyxl

class InvoiceParser:
    def __init__(self):
        self.supported_formats = {
            'pdf': self.parse_pdf,
            'docx': self.parse_docx,
            'xlsx': self.parse_excel,
            'xls': self.parse_excel
        }
    
    def parse(self, file_path):
        """Parse invoice file in various formats"""
        file_extension = file_path.split('.')[-1].lower()
        
        if file_extension not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        return self.supported_formats[file_extension](file_path)
    
    def parse_pdf(self, file_path):
        """Parse PDF invoice using multiple methods"""
        result = {}
        
        # Try invoice2data first (template-based approach)
        try:
            data = extract_data(file_path)
            if data:
                result['invoice2data'] = data
        except Exception as e:
            print(f"invoice2data failed: {e}")
        
        # Extract text with PyMuPDF
        try:
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            result['text'] = text
        except Exception as e:
            print(f"PyMuPDF failed: {e}")
        
        # Extract tables with Camelot
        try:
            tables = camelot.read_pdf(file_path)
            if tables.n > 0:
                result['tables'] = []
                for i, table in enumerate(tables):
                    result['tables'].append(table.df.to_dict())
        except Exception as e:
            print(f"Camelot failed: {e}")
        
        return result
    
    def parse_docx(self, file_path):
        """Parse DOCX invoice"""
        result = {}
        
        try:
            doc = Document(file_path)
            
            # Extract text from paragraphs
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text)
            result['paragraphs'] = paragraphs
            
            # Extract tables
            tables = []
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = [cell.text for cell in row.cells]
                    table_data.append(row_data)
                tables.append(table_data)
            result['tables'] = tables
            
        except Exception as e:
            print(f"python-docx failed: {e}")
        
        return result
    
    def parse_excel(self, file_path):
        """Parse Excel invoice"""
        result = {}
        
        # Use pandas for data analysis
        try:
            df = pd.read_excel(file_path)
            result['pandas'] = {
                'data': df.to_dict(orient="records"),
                'shape': df.shape
            }
        except Exception as e:
            print(f"pandas failed: {e}")
        
        # Use openpyxl for detailed cell access
        try:
            wb = openpyxl.load_workbook(file_path, data_only=True)
            sheet = wb.active
            
            # Extract header information (common in invoices)
            headers = {}
            for row in range(1, 10):  # Usually headers are in first rows
                for col in range(1, 10):  # And first columns
                    cell_value = sheet.cell(row=row, column=col).value
                    if cell_value:
                        headers[f"{chr(64+col)}{row}"] = str(cell_value)
            
            result['headers'] = headers
            
            # Try to detect table structure
            table_data = []
            table_start_row = None
            
            # Look for table headers (common patterns in invoices)
            for row in range(1, sheet.max_row + 1):
                row_values = [sheet.cell(row=row, column=col).value for col in range(1, sheet.max_column + 1)]
                if any(keyword in str(value).lower() if value else False 
                       for value in row_values 
                       for keyword in ['item', 'description', 'quantity', 'price', 'amount']):
                    table_start_row = row
                    break
            
            if table_start_row:
                headers = [str(sheet.cell(row=table_start_row, column=col).value) 
                          for col in range(1, sheet.max_column + 1)]
                
                for row in range(table_start_row + 1, sheet.max_row + 1):
                    row_values = [sheet.cell(row=row, column=col).value 
                                 for col in range(1, sheet.max_column + 1)]
                    
                    # Skip empty rows
                    if not any(row_values):
                        continue
                    
                    # Check if this might be a total row
                    row_text = ' '.join(str(v) for v in row_values if v)
                    if any(keyword in row_text.lower() 
                          for keyword in ['total', 'sum', 'subtotal', 'vat', 'tax']):
                        result['totals'] = dict(zip(headers, row_values))
                        continue
                    
                    table_data.append(dict(zip(headers, row_values)))
                
                result['table_data'] = table_data
        
        except Exception as e:
            print(f"openpyxl failed: {e}")
        
        return result

# Przykład użycia
if __name__ == "__main__":
    parser = InvoiceParser()
    
    # Przykładowe parsowanie faktury PDF
    pdf_result = parser.parse("faktura.pdf")
    with open("faktura_pdf_result.json", "w", encoding="utf-8") as f:
        json.dump(pdf_result, f, indent=2, default=str)
    
    # Przykładowe parsowanie faktury DOCX
    docx_result = parser.parse("faktura.docx")
    with open("faktura_docx_result.json", "w", encoding="utf-8") as f:
        json.dump(docx_result, f, indent=2, default=str)
    
    # Przykładowe parsowanie faktury Excel
    excel_result = parser.parse("faktura.xlsx")
    with open("faktura_excel_result.json", "w", encoding="utf-8") as f:
        json.dump(excel_result, f, indent=2, default=str)
```

## Podsumowanie

Wybór odpowiedniej biblioteki do parsowania dokumentów z załączników, takich jak faktury, zależy od formatu dokumentu, złożoności jego struktury oraz konkretnych wymagań dotyczących ekstrakcji danych. Dla każdego formatu istnieją wyspecjalizowane biblioteki, które oferują najlepszą wydajność i dokładność.

Rekomendujemy podejście wielowarstwowe, które łączy najlepsze biblioteki dla każdego formatu z systemami multi-agentowymi do walidacji i korekty danych. Takie podejście zapewnia wysoką dokładność ekstrakcji danych z różnych formatów dokumentów, elastyczność w obsłudze różnych układów faktur oraz niezawodność dzięki walidacji przez systemy AI.

Przedstawione w raporcie biblioteki i przykładowy kod stanowią solidną podstawę do budowy systemu automatycznego przetwarzania faktur i innych dokumentów biznesowych, który może być łatwo zintegrowany z istniejącymi systemami księgowymi i bazami danych.
